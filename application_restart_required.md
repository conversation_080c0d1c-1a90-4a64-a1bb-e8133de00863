# AI推荐功能修复 - 需要重启应用

## 当前状况
从日志可以看出，应用仍然在调用旧的方法：
```
2025-08-04 17:38:37.648 DEBUG 35708 --- [nio-8080-exec-2] com.sky.mapper.DishMapper.listOfDish     : ==>  Preparing: select * from dish where category_id=? and status = ?
```

这说明应用还没有加载新的代码修改。

## 问题原因
1. **代码修改未生效**: 虽然我们修改了代码，但应用可能还在使用旧的编译版本
2. **需要重新编译**: Java应用需要重新编译才能使用新的代码
3. **需要重启应用**: 即使重新编译，也需要重启应用才能加载新的类

## 解决方案

### 1. 立即解决方案
我已经修改了代码，使用现有的 `listAllAvailableDishes()` 方法：

```java
@Override
public List<AiRecommendVO> getAllAvailableDishes() {
    try {
        log.info("开始获取所有可用菜品数据");
        
        // 直接查询所有起售状态的菜品（包含分类名称）
        List<DishVO> dishes = dishMapper.listAllAvailableDishes();
        log.info("查询到{}个起售状态的菜品", dishes.size());
        
        // 为每个菜品获取规格信息
        for (DishVO dish : dishes) {
            List<DishFlavor> flavors = dishFlavorMapper.getFlavorsByDishId(dish.getId());
            dish.setFlavors(flavors);
            log.debug("菜品{}有{}个规格", dish.getName(), flavors.size());
        }

        List<AiRecommendVO> result = dishes.stream()
                .map(this::convertToAiRecommendVO)
                .collect(Collectors.toList());
        
        log.info("成功转换{}个菜品为AI推荐数据", result.size());
        return result;
    } catch (Exception e) {
        log.error("获取菜品数据失败", e);
        return new ArrayList<>();
    }
}
```

### 2. 需要执行的操作

#### 重新编译项目
```bash
# 在项目根目录执行
mvn clean compile
# 或者
mvn clean package
```

#### 重启应用
停止当前运行的应用，然后重新启动。

### 3. 验证修复

重启后，应该看到以下日志：
```
INFO: 开始获取所有可用菜品数据
DEBUG: ==>  Preparing: select d.*, c.name as categoryName from dish d left join category c on d.category_id = c.id where d.status = 1
DEBUG: ==> Parameters: 
INFO: 查询到X个起售状态的菜品
INFO: 成功转换X个菜品为AI推荐数据
```

### 4. 数据库检查

如果重启后仍然没有数据，请检查数据库：

```sql
-- 检查是否有起售状态的菜品
SELECT COUNT(*) FROM dish WHERE status = 1;

-- 查看所有菜品的状态
SELECT id, name, status FROM dish;

-- 检查菜品规格数据
SELECT d.name, df.name as flavor_name, df.value 
FROM dish d 
LEFT JOIN dish_flavor df ON d.id = df.dish_id 
WHERE d.status = 1;
```

## 修改的文件
1. `sky-take-out/sky-server/src/main/java/com/sky/mapper/DishMapper.java`
   - 添加了 `listAllEnabledDishes()` 方法

2. `sky-take-out/sky-server/src/main/java/com/sky/service/impl/AiRecommendServiceImpl.java`
   - 修改了 `getAllAvailableDishes()` 方法
   - 添加了详细的日志记录

## 预期结果
重启应用后，AI推荐功能应该能够：
1. 正确查询到所有起售状态的菜品
2. 获取每个菜品的规格信息
3. 返回包含规格数据的推荐结果
4. 前端正确显示规格选择功能

## 注意事项
- 确保数据库中有 `status = 1` 的菜品数据
- 检查菜品是否有对应的规格数据
- 重启后观察日志，确认新的查询方法被调用
