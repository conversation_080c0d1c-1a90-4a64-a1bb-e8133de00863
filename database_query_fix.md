# AI推荐数据库查询问题修复

## 问题描述
AI推荐功能在获取菜品数据时出现SQL查询错误：
```sql
select * from dish where category_id=? and status = ?
Parameters: null, 1(Integer)
```

由于 `category_id=null`，导致查询不到任何菜品数据，返回结果为空。

## 问题原因分析

### 原始代码问题
```java
// 原始代码
Dish dish = new Dish();
dish.setStatus(StatusConstant.ENABLE); // 只查询起售状态的菜品
List<DishVO> dishes = dishService.listWithFlavor(dish);
```

### 问题根源
1. `dishService.listWithFlavor(dish)` 内部调用 `dishMapper.listOfDish(dish)`
2. `listOfDish` 方法的SQL是：`select * from dish where category_id=#{categoryId} and status = #{status}`
3. 当 `categoryId` 为 null 时，SQL条件变成 `category_id=null`，不会匹配任何记录

## 解决方案

### 1. 添加新的Mapper方法
在 `DishMapper.java` 中添加：
```java
/**
 * 查询所有起售状态的菜品（不带分类条件）
 */
@Select("select * from dish where status = 1")
List<Dish> listAllEnabledDishes();
```

### 2. 修改Service实现
在 `AiRecommendServiceImpl.java` 中：
```java
@Override
public List<AiRecommendVO> getAllAvailableDishes() {
    try {
        // 查询所有起售状态的菜品
        List<Dish> dishes = dishMapper.listAllEnabledDishes();
        
        // 转换为DishVO并获取规格信息
        List<DishVO> dishVOList = new ArrayList<>();
        for (Dish dish : dishes) {
            DishVO dishVO = new DishVO();
            // 复制基本属性
            dishVO.setId(dish.getId());
            dishVO.setName(dish.getName());
            // ... 其他属性
            
            // 获取规格信息
            List<DishFlavor> flavors = dishFlavorMapper.getFlavorsByDishId(dish.getId());
            dishVO.setFlavors(flavors);
            
            dishVOList.add(dishVO);
        }

        return dishVOList.stream()
                .map(this::convertToAiRecommendVO)
                .collect(Collectors.toList());
    } catch (Exception e) {
        log.error("获取菜品数据失败", e);
        return new ArrayList<>();
    }
}
```

## 修复后的SQL查询
```sql
-- 查询所有起售状态的菜品
select * from dish where status = 1

-- 为每个菜品查询规格信息
select * from dish_flavor where dish_id = ?
```

## 验证方法

### 1. 检查日志
修复后应该看到类似的日志：
```
INFO: 开始AI推荐，用户输入：，选择标签：[清淡口味]
DEBUG: ==>  Preparing: select * from dish where status = 1
DEBUG: ==> Parameters: 
DEBUG: <==      Total: X (X为实际菜品数量)
```

### 2. 测试步骤
1. 确保数据库中有 `status = 1` 的菜品数据
2. 调用AI推荐接口
3. 检查返回的菜品数据是否包含规格信息
4. 验证前端能正确显示规格选择功能

## 注意事项
1. 确保数据库中有起售状态的菜品（`status = 1`）
2. 检查菜品是否有对应的规格数据（`dish_flavor` 表）
3. 验证前端能正确解析返回的规格数据

## 相关文件修改
- `sky-take-out/sky-server/src/main/java/com/sky/mapper/DishMapper.java`
- `sky-take-out/sky-server/src/main/java/com/sky/service/impl/AiRecommendServiceImpl.java`
