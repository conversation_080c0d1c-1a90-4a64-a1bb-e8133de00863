// API接口文件
const { getApiUrl } = require('../utils/config.js');

// 获取存储的token
function getToken() {
  try {
    return wx.getStorageSync('token') || '';
  } catch (e) {
    return '';
  }
}

// 根据套餐ID查询套餐菜品
function querySetmealDishById(params) {
  return new Promise((resolve, reject) => {
    const token = getToken();
    const headers = {
      'Content-Type': 'application/json'
    };

    // 如果有token，添加到header中
    if (token) {
      headers['authentication'] = token;
    }

    wx.request({
      url: `http://localhost:8080/user/setmeal/dish/${params.id}`,
      method: 'GET',
      header: headers,
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          reject(new Error(`请求失败: ${res.statusCode}`));
        }
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
}

// 加入购物车
function addToShoppingCart(params) {
  return new Promise((resolve, reject) => {
    const token = getToken();
    const headers = {
      'Content-Type': 'application/json'
    };

    // 如果有token，添加到header中
    if (token) {
      headers['authentication'] = token;
    }

    wx.request({
      url: 'http://localhost:8080/user/shoppingCart/add',
      method: 'POST',
      header: headers,
      data: params,
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          reject(new Error(`请求失败: ${res.statusCode}`));
        }
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
}

// 查询购物车
function getShoppingCart() {
  return new Promise((resolve, reject) => {
    const token = getToken();
    const headers = {
      'Content-Type': 'application/json'
    };

    // 如果有token，添加到header中
    if (token) {
      headers['authentication'] = token;
    }

    wx.request({
      url: 'http://localhost:8080/user/shoppingCart/list',
      method: 'GET',
      header: headers,
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          reject(new Error(`请求失败: ${res.statusCode}`));
        }
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
}

// 减少购物车商品
function subShoppingCart(params) {
  return new Promise((resolve, reject) => {
    const token = getToken();
    const headers = {
      'Content-Type': 'application/json'
    };

    // 如果有token，添加到header中
    if (token) {
      headers['authentication'] = token;
    }

    wx.request({
      url: 'http://localhost:8080/user/shoppingCart/sub',
      method: 'POST',
      header: headers,
      data: params,
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          reject(new Error(`请求失败: ${res.statusCode}`));
        }
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
}

// 用户登录
function userLogin(code) {
  return new Promise((resolve, reject) => {
    wx.request({
      url: 'http://localhost:8080/user/user/login',
      method: 'POST',
      header: {
        'Content-Type': 'application/json'
      },
      data: {
        code: code
      },
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          reject(new Error(`请求失败: ${res.statusCode}`));
        }
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
}

module.exports = {
  querySetmealDishById,
  addToShoppingCart,
  getShoppingCart,
  subShoppingCart,
  userLogin
};
