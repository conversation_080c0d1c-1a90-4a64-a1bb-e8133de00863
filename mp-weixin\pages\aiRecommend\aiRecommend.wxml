<view class="ai-recommend-container">
  <!-- 导航栏 -->
  <view class="nav-bar">
    <view class="nav-back" bindtap="goBack">
      <image class="back-icon" src="../../static/back.svg" mode="aspectFit"></image>
    </view>
    <view class="nav-title">AI智能推荐</view>
    <view class="nav-placeholder"></view>
  </view>

  <!-- 主要内容区域 -->
  <view class="content">
    <!-- 输入区域 -->
    <view class="input-section">
      <view class="input-title">告诉我您的需求</view>
      <view class="input-subtitle">描述您想要的菜品类型、口味偏好等</view>
      
      <view class="input-container">
        <textarea 
          class="input-textarea" 
          placeholder="例如：我想要一些清淡的菜，不要太辣的，适合老人吃的..."
          value="{{userInput}}"
          bindinput="onInputChange"
          maxlength="200"
          auto-height
        ></textarea>
        <view class="input-counter">{{userInput.length}}/200</view>
      </view>

      <!-- 快捷标签 -->
      <view class="quick-tags">
        <view class="tags-title">快捷选择：</view>
        <view class="tags-container">
          <view 
            class="tag-item {{item.selected ? 'selected' : ''}}" 
            wx:for="{{quickTags}}" 
            wx:key="id"
            bindtap="selectTag"
            data-index="{{index}}"
          >
            {{item.name}}
          </view>
        </view>
      </view>

      <!-- 推荐按钮 -->
      <view class="recommend-btn {{userInput.length > 0 || hasSelectedTags ? 'active' : ''}}" bindtap="getRecommendation">
        <image class="btn-icon" src="../../static/ai-icon.svg" mode="aspectFit"></image>
        <text class="btn-text">获取AI推荐</text>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-section" wx:if="{{isLoading}}">
      <view class="loading-card">
        <view class="loading-header">
          <view class="ai-thinking-icon">🤖</view>
          <text class="loading-title">AI智能分析中</text>
        </view>
        <view class="loading-content">
          <view class="loading-dots-container">
            <view class="loading-dot {{loadingDotIndex === 0 ? 'active' : ''}}"></view>
            <view class="loading-dot {{loadingDotIndex === 1 ? 'active' : ''}}"></view>
            <view class="loading-dot {{loadingDotIndex === 2 ? 'active' : ''}}"></view>
            <view class="loading-dot {{loadingDotIndex === 3 ? 'active' : ''}}"></view>
            <view class="loading-dot {{loadingDotIndex === 4 ? 'active' : ''}}"></view>
          </view>
          <text class="loading-subtitle">正在为您推荐最适合的美食...</text>
        </view>
        <view class="loading-progress">
          <view class="progress-bar">
            <view class="progress-fill"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 推荐结果 -->
    <view class="result-section" wx:if="{{recommendResult.length > 0 && !isLoading}}">
      <view class="result-header">
        <view class="result-header-left">
          <image class="ai-avatar" src="../../static/ai-avatar.svg" mode="aspectFit"></image>
          <view class="result-title">AI为您推荐以下菜品</view>
        </view>
        <view class="result-header-right">
          <view class="new-search-btn" bindtap="newSearch">
            <image class="refresh-icon" src="../../static/refresh.svg" mode="aspectFit"></image>
            <text class="new-search-text">重新搜索</text>
          </view>
        </view>
      </view>

      <view class="dish-list">
        <view
          class="dish-item"
          wx:for="{{recommendResult}}"
          wx:key="id"
          bindtap="openDetailHandle"
          data-dish="{{item}}"
        >
          <view class="dish-image-container">
            <image class="dish-image" src="{{item.image}}" mode="aspectFill"></image>
            <view class="recommend-badge">AI推荐</view>
          </view>
          
          <view class="dish-info">
            <view class="dish-name">{{item.name}}</view>
            <view class="dish-description">{{item.description}}</view>
            <view class="recommend-reason">
              <image class="reason-icon" src="../../static/reason-icon.svg" mode="aspectFit"></image>
              <text class="reason-text">{{item.recommendReason}}</text>
            </view>
            <view class="dish-footer">
              <view class="dish-price">
                <text class="price-symbol">￥</text>
                <text class="price-value">{{item.price}}</text>
              </view>
              <view class="dish-actions">
                <view class="add-to-cart" bindtap="addToCart" data-dish="{{item}}" catchtap="true">
                  <image class="cart-icon" src="../../static/add-cart.svg" mode="aspectFit"></image>
                  <text>加入购物车</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 重新推荐按钮 -->
      <view class="retry-section">
        <view class="retry-btn" bindtap="getRecommendation">
          <image class="retry-icon" src="../../static/refresh.svg" mode="aspectFit"></image>
          <text>重新推荐</text>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-section" wx:if="{{!isLoading && recommendResult.length === 0 && hasSearched}}">
      <image class="empty-icon" src="../../static/no_order.png" mode="aspectFit"></image>
      <view class="empty-text">暂时没有找到合适的推荐</view>
      <view class="empty-subtitle">请尝试调整您的需求描述</view>
    </view>
  </view>

  <!-- 菜品详情弹窗 -->
  <view hidden="{{!openDetailPop}}" class="pop_mask" style="z-index:9999;">
    <block wx:if="{{dishDetailes.type==1}}">
      <view class="dish_detail_pop">
        <image class="div_big_image" mode="aspectFill" src="{{dishDetailes.image}}"></image>
        <view class="title">{{dishDetailes.name}}</view>
        <view class="desc">{{dishDetailes.description}}</view>
        <view class="but_item">
          <view class="price"><text class="ico">￥</text>{{dishDetailes.price}}</view>
          <block wx:if="{{dishDetailes.flavors.length===0&&dishDetailes.dishNumber>0}}">
            <view class="active">
              <image class="dish_red" src="../../static/btn_red.png" mode bindtap="redDishAction" data-dish="{{dishDetailes}}"></image>
              <text class="dish_number">{{dishDetailes.dishNumber}}</text>
              <image class="dish_add" src="../../static/btn_add.png" mode bindtap="addDishAction" data-dish="{{dishDetailes}}"></image>
            </view>
          </block>
          <block wx:if="{{dishDetailes.flavors.length>0}}">
            <view class="active">
              <view bindtap="moreNormDataesHandle" data-dish="{{dishDetailes}}" class="dish_card_add">选择规格</view>
            </view>
          </block>
          <block wx:if="{{dishDetailes.dishNumber===0&&dishDetailes.flavors.length===0}}">
            <view class="active">
              <view bindtap="addDishAction" data-dish="{{dishDetailes}}" class="dish_card_add">加入购物车</view>
            </view>
          </block>
        </view>
        <view bindtap="closeDetailPop" class="close">
          <image class="close_img" src="../../static/but_close.png" mode></image>
        </view>
      </view>
    </block>
    <block wx:else>
      <view class="dish_detail_pop">
        <scroll-view class="dish_items" scroll-y="true" scroll-top="0rpx">
          <block wx:for="{{dishMealData}}" wx:for-item="item" wx:for-index="index" wx:key="index">
            <view class="dish_item">
              <image class="div_big_image" src="{{item.image}}" mode></image>
              <view class="title">{{item.name}}<text>X{{item.copies}}</text></view>
              <view class="desc">{{item.description}}</view>
            </view>
          </block>
        </scroll-view>
        <view class="but_item">
          <view class="price"><text class="ico">￥</text>{{dishDetailes.price}}</view>
          <block wx:if="{{dishDetailes.dishNumber&&dishDetailes.dishNumber>0}}">
            <view class="active">
              <image class="dish_red" src="../../static/btn_red.png" mode bindtap="redDishAction" data-dish="{{dishDetailes}}"></image>
              <text class="dish_number">{{dishDetailes.dishNumber}}</text>
              <image class="dish_add" src="../../static/btn_add.png" mode bindtap="addDishAction" data-dish="{{dishDetailes}}"></image>
            </view>
          </block>
          <block wx:else>
            <block wx:if="{{dishDetailes.dishNumber==0}}">
              <view class="active">
                <view bindtap="addDishAction" data-dish="{{dishDetailes}}" class="dish_card_add">加入购物车</view>
              </view>
            </block>
          </block>
        </view>
        <view bindtap="closeDetailPop" class="close">
          <image class="close_img" src="../../static/but_close.png" mode></image>
        </view>
      </view>
    </block>
  </view>
</view>
