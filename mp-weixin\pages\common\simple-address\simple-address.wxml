<block wx:if="{{showPopup}}"><view data-event-opts="{{[['touchmove',[['clear',['$event']]]]]}}" class="simple-address data-v-6d425f7a" catchtouchmove="__e"><block wx:if="{{maskClick}}"><view data-event-opts="{{[['touchmove',[['clear',['$event']]]],['tap',[['hideMask',[true]]]]]}}" class="{{['simple-address-mask','data-v-6d425f7a',ani+'-mask',animation?'mask-ani':'']}}" style="{{'background-color:'+(maskBgColor)+';'}}" catchtouchmove="__e" bindtap="__e"></view></block><view class="{{['simple-address-content','simple-address--fixed','data-v-6d425f7a',type,ani+'-content',animation?'content-ani':'']}}"><view class="simple-address__header data-v-6d425f7a"><view data-event-opts="{{[['tap',[['pickerCancel',['$event']]]]]}}" class="simple-address__header-btn-box data-v-6d425f7a" bindtap="__e"><text class="simple-address__header-text data-v-6d425f7a">取消</text></view><view data-event-opts="{{[['tap',[['pickerConfirm',['$event']]]]]}}" class="simple-address__header-btn-box data-v-6d425f7a" bindtap="__e"><text class="simple-address__header-text data-v-6d425f7a" style="{{'color:'+(themeColor)+';'}}">确定</text></view></view><view class="simple-address__box data-v-6d425f7a"><picker-view class="simple-address-view data-v-6d425f7a" indicator-style="height: 70rpx;" value="{{pickerValue}}" data-event-opts="{{[['change',[['pickerChange',['$event']]]]]}}" bindchange="__e"><picker-view-column class="data-v-6d425f7a"><block wx:for="{{provinceDataList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="picker-item data-v-6d425f7a">{{item.label}}</view></block></picker-view-column><picker-view-column class="data-v-6d425f7a"><block wx:for="{{cityDataList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="picker-item data-v-6d425f7a">{{item.label}}</view></block></picker-view-column><picker-view-column class="data-v-6d425f7a"><block wx:for="{{areaDataList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="picker-item data-v-6d425f7a">{{item.label}}</view></block></picker-view-column></picker-view></view></view></view></block>