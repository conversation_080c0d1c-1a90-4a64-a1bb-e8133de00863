@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.navBar.data-v-751e51d6 {
  position: fixed;
  display: flex;
  z-index: 9;
  top: 0;
  left: 0;
  height: 160rpx;
  width: 100vw;
  box-sizing: border-box;
  height: 304rpx;
  opacity: 1;
  background: linear-gradient(184deg, rgba(0, 0, 0, 0.35) 25%, rgba(51, 51, 51, 0) 96%);
}
.navBar .leftNav .back.data-v-751e51d6 {
  width: 88rpx;
  height: 88rpx;
}
.navBar .centerNav.data-v-751e51d6 {
  width: calc(100vw - 176rpx);
  text-align: center;
  line-height: 88rpx;
  font-size: 36rpx;
  color: #fff;
}
.navBar .logo.data-v-751e51d6 {
  height: 66rpx;
  width: 184rpx;
}
.navBar .index_bg.data-v-751e51d6 {
  width: 750rpx;
  height: 304rpx;
}
.navBar .test_image.data-v-751e51d6 {
  height: 56rpx;
  width: 56rpx;
}
.navBar .person-box.data-v-751e51d6 {
  position: fixed;
  left: 38rpx;
  z-index: 9999;
  top: 100rpx;
  display: flex;
  align-items: center;
}
.navBar .person-title.data-v-751e51d6 {
  flex: 1;
  height: 36rpx;
  font-size: 26rpx;
  font-family: PingFangSC, PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  color: #ffffff;
  line-height: 36rpx;
  letter-spacing: 0px;
  margin-left: 14rpx;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.nonet_content.data-v-751e51d6 {
  padding-top: 260rpx;
}
.nonet_content .success_info.data-v-751e51d6 {
  text-align: center;
}
.nonet_content .success_info .success_icon.data-v-751e51d6 {
  width: 300rpx;
  height: 300rpx;
  text-align: center;
}
.nonet_content .success_info .success_title.data-v-751e51d6 {
  font-size: 48rpx;
}
.nonet_content .success_info .success_desc.data-v-751e51d6 {
  font-size: 32rpx;
  margin-bottom: 40rpx;
  color: #818693;
}
.nonet_content .success_info .go_dish.data-v-751e51d6 {
  position: relative;
  font-size: 30rpx;
  margin: 0 auto;
  width: 248rpx;
  line-height: 72rpx;
  margin-top: 20rpx;
  background: linear-gradient(144deg, #ffda05 18%, #ffb302 80%);
  border-radius: 36rpx;
}

