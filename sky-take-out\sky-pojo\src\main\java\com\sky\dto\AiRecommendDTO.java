package com.sky.dto;

import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * AI推荐请求DTO
 */
@Data
public class AiRecommendDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户输入的需求描述
     */
    private String userInput;

    /**
     * 用户选择的快捷标签
     */
    private List<String> selectedTags;

    /**
     * 用户ID（可选，用于个性化推荐）
     */
    private Long userId;

    /**
     * 商户ID（可选，用于多商户场景）
     */
    private Long merchantId;

    /**
     * 推荐数量限制（默认5个）
     */
    private Integer limit = 5;
}
