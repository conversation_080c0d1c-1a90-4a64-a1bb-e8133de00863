package com.sky.dto;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(description = "员工分页查询时传递的数据模型")
public class EmployeePageQueryDTO implements Serializable {

    //员工姓名
    @ApiModelProperty("员工姓名")
    private String name;

    //以下数据可以设置默认值，并且不能使用int!!!!
    //页码
    @ApiModelProperty("页码")
    private Integer page = 1;

    //每页显示记录数
    @ApiModelProperty("每页记录数")
    private Integer pageSize = 10;

}
