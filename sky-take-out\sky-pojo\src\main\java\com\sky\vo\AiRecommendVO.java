package com.sky.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * AI推荐结果VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiRecommendVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 菜品ID
     */
    private Long dishId;

    /**
     * 菜品名称
     */
    private String name;

    /**
     * 菜品描述
     */
    private String description;

    /**
     * 菜品价格
     */
    private BigDecimal price;

    /**
     * 菜品图片
     */
    private String image;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * AI推荐理由
     */
    private String recommendReason;

    /**
     * 推荐分数（0-100）
     */
    private Integer score;

    /**
     * 菜品状态（0停售 1起售）
     */
    private Integer status;
}
