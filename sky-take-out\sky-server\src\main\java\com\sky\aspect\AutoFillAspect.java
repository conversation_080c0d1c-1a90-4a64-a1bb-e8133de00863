package com.sky.aspect;


//--公共字段自动填充切面类


import com.sky.anno.AutoFill;
import com.sky.constant.AutoFillConstant;
import com.sky.context.BaseContext;
import com.sky.enumeration.OperationType;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.Arrays;

@Slf4j//日志
@Component//不交给容器管理
@Aspect//声明切面类
public class AutoFillAspect {

    @Before("@annotation(com.sky.anno.AutoFill)")//在方法生效之前进行操作
    public void autoFill(JoinPoint joinPoint) {
        log.info("自定义注解开始生效......");
        //1.获取目标方法上面的注解，并拿到注解里面的属性值
        //调用方法签名（记得强转），再拿到方法对象，通过方法对象进行注解反射拿到注解，通过注解拿到注解的值
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        AutoFill autoFill = method.getAnnotation(AutoFill.class);
        OperationType operationType = autoFill.value();
        //2.获取到目标方法的参数对象
        //通过参数JoinPoint获取参数数组，将参数数组转换为Object对象从而拿到实体对象 （为空或为0判断）
        Object[] args = joinPoint.getArgs();
        if (args == null || args.length == 0) {
            return;
        }
        Object entity = args[0];

        try {
            if (operationType == OperationType.INSERT) {
                //3.判断注解中属性值，如果是INSERT，补充字段：创建时间，创建人、更新时间、更新人
                Method setCreateTime = entity.getClass().getDeclaredMethod(AutoFillConstant.SET_CREATE_TIME, LocalDateTime.class);
                setCreateTime.invoke(entity, LocalDateTime.now());//设置创建时间
                Method setCreateUser = entity.getClass().getDeclaredMethod(AutoFillConstant.SET_CREATE_USER, Long.class);
                setCreateUser.invoke(entity, BaseContext.getCurrentId());//设置创建人
                Method setUpdateTime = entity.getClass().getDeclaredMethod(AutoFillConstant.SET_UPDATE_TIME, LocalDateTime.class);
                setUpdateTime.invoke(entity, LocalDateTime.now());//设置更新时间
                Method setUpdateUser = entity.getClass().getDeclaredMethod(AutoFillConstant.SET_UPDATE_USER, Long.class);
                setUpdateUser.invoke(entity, BaseContext.getCurrentId());//设置更新人
            } else if (operationType == OperationType.UPDATE) {
                //4.如果是UPDATE就补充字段：更新时间、更新人
                Method setUpdateTime = entity.getClass().getDeclaredMethod(AutoFillConstant.SET_UPDATE_TIME, LocalDateTime.class);
                setUpdateTime.invoke(entity, LocalDateTime.now());//设置更新时间
                Method setUpdateUser = entity.getClass().getDeclaredMethod(AutoFillConstant.SET_UPDATE_USER, Long.class);
                setUpdateUser.invoke(entity, BaseContext.getCurrentId());//设置更新人
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }


    }
}
