package com.sky.controller.admin;


import com.sky.dto.DishDTO;
import com.sky.dto.DishPageQueryDTO;
import com.sky.entity.Dish;
import com.sky.result.PageResult;
import com.sky.result.Result;
import com.sky.service.DishService;
import com.sky.vo.DishVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

@Slf4j
@Api(tags = "菜品管理的相关接口")
@RequestMapping("/admin/dish")
@RestController
public class DishController {
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private DishService dishService;
    /**
     * 新增菜品
     * @param dishDTO
     * @return
     */

    @PostMapping
    public Result addDish(@RequestBody DishDTO dishDTO){
        log.info("新增菜品数据：{}",dishDTO);
        dishService.addDish(dishDTO);
        //新增菜品完成后，根据菜品对应的categoryId删除缓存
        redisTemplate.delete("dish_"+dishDTO.getCategoryId());
        return Result.success();
    }

    /**
     * 菜品分页查询
     * @param dto
     * @return
     */
    @ApiOperation("菜品分页查询")
    @GetMapping("/page")
    public Result<PageResult> page(DishPageQueryDTO dto){
        log.info("菜品分页查询数据：{}",dto);
        PageResult pageResult=dishService.list(dto);
        return Result.success(pageResult);
    }

    /**
     * 删除菜品
     * @param ids
     * @return
     */
    @ApiOperation("删除菜品")
    @DeleteMapping
    public Result deleteDish(@RequestParam List<Long> ids){
        log.info("删除菜品：{}",ids);
        dishService.deleteDish(ids);
        //删除菜品之后，还要查询对应的分类去进行删除，很不方便，所以删除分类缓存全部执行一次
        Set keys = redisTemplate.keys("*dish_*");//查找dish_模糊搜索
        redisTemplate.delete(keys);//将结果集合批量删除

        return Result.success();
    }

    /**
     * 根据id查询菜品，用于数据回显
     * @param id
     * @return
     */
    @ApiOperation("根据id查询菜品")
    @GetMapping("/{id}")
    public Result getDishById(@PathVariable Long id){
        log.info("查询菜品的id：{}",id);
        DishVO dishVO =dishService.getDishById(id);
        return Result.success(dishVO);
    }

    /**
     * 根据分类id查询菜品
     * @param categoryId
     * @return
     */
    @ApiOperation("根据分类id查询菜品")
    @GetMapping("/list")
    public Result getDishByCategoryId(@RequestParam("categoryId")Long categoryId){
        log.info("需要根据分类id:{}，查询菜品中...",categoryId);
        List<DishVO> dishVO=dishService.getDishByCategoryId(categoryId);
        return Result.success(dishVO);
    }




    /**
     * 修改菜品
     * @param dto
     * @return
     */
    @ApiOperation("修改菜品")
    @PutMapping
    public Result updateDish(@RequestBody DishDTO dto){
        log.info("修改的菜品原数据为：{}",dto);
        dishService.updateDish(dto);
        //修改菜品完成后，你要删除原分类的菜品缓存和新分类的菜品缓存，十分不便于查找，故全面删除缓存
        Set keys = redisTemplate.keys("*dish_*");//查找dish_模糊搜索
        redisTemplate.delete(keys);//将结果集合批量删除
        return Result.success();
    }

    /**
     * 菜品起售、停售
     * @param status
     * @param id
     * @return
     */
    @ApiOperation("菜品起售、停售")
    @PostMapping("/status/{status}")
    public Result enableOrDisable(@PathVariable Integer status,
                                  @RequestParam Long id){
        log.info("需要修改id为:{} ,状态: {}",id,status);
        dishService.enableOrDisable(status,id);
        //起售停售的菜品可能在多个分类之中，不方便查找，故全部缓存都将删除
        Set keys = redisTemplate.keys("*dish_*");//查找dish_模糊搜索
        redisTemplate.delete(keys);//将结果集合批量删除
        return Result.success();
    }
}
