package com.sky.controller.admin;

import com.github.pagehelper.Page;
import com.sky.dto.SetmealDTO;
import com.sky.dto.SetmealPageQueryDTO;
import com.sky.result.PageResult;
import com.sky.result.Result;
import com.sky.service.SetmealService;
import com.sky.vo.SetmealVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.message.ReusableMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 套餐管理
 */
@Slf4j
@RestController
@RequestMapping("/admin/setmeal")
@Api(tags = "套餐管理")
public class SetmealController {
    @Autowired
    private SetmealService setmealService;

    /**
     * 新增套餐
     *
     * @param dto
     * @return
     */
    @CacheEvict(cacheNames = "setmeal",key = "#dto.categoryId")
    @ApiOperation("新增套餐")
    @PostMapping
    public Result addSetmeal(@RequestBody SetmealDTO dto) {
        log.info("上传的套餐信息为：{}", dto);
        setmealService.addSetmeal(dto);
        return Result.success();
    }


    /**
     * 分页查询
     *
     * @param dto
     * @return
     */
    @ApiOperation("分页查询")
    @GetMapping("/page")
    public Result page(SetmealPageQueryDTO dto) {
        log.info("开始进行分页查询:{}", dto);
        PageResult pageResult = setmealService.page(dto);
        return Result.success(pageResult);
    }

    /**
     * 批量删除套餐
     *
     * @param ids
     * @return
     */
    @CacheEvict(cacheNames = "setmeal",allEntries = true)
    @ApiOperation("批量删除套餐")
    @DeleteMapping
    public Result deleteBatch(@RequestParam("ids") List<Long> ids) {
        log.info("删除套餐的id:{}", ids);
        setmealService.deleteBatch(ids);
        return Result.success();
    }

    /**
     * 根据id查询套餐
     *
     * @param id
     * @return
     */
    @ApiOperation("根据id查询套餐")
    @GetMapping("/{id}")
    public Result getSetmealById(@PathVariable Long id) {
        log.info("根据id:{} 查询套餐", id);
        SetmealVO setmealVO = setmealService.getSetmealById(id);
        return Result.success(setmealVO);
    }

    /**
     * 套餐起售或停售
     * @param status
     * @param id
     * @return
     */
    @CacheEvict(cacheNames = "setmeal",allEntries = true)
    @ApiOperation("套餐起售、停售")
    @PostMapping("/status/{status}")
    public Result enableOrDisable(@PathVariable Integer status,
                                  @RequestParam Long id) {
        log.info("当前套餐状态:{} ， 当前套餐id:{}",status,id);
        setmealService.enableOrDisable(status,id);
        return Result.success();
    }

    /**
     * 修改套餐
     * @param dto
     * @return
     */
    //旧套餐分类id你拿不到，你只能拿到新的分类id，故全部清除缓存
    @CacheEvict(cacheNames = "setmeal",allEntries = true)
    @ApiOperation("修改套餐")
    @PutMapping
    public Result updateSetmeal(@RequestBody SetmealDTO dto){
        log.info("更新套餐的数据：{}",dto);
        setmealService.updateSetmeal(dto);
        return Result.success();
    }
}
