package com.sky.controller.admin;


import com.sky.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.web.bind.annotation.*;

/**
 * 店铺状态管理
 */
@Slf4j
//注意不能重名！
@RestController("adminShopController")
@Api(tags = "店铺状态管理")
@RequestMapping("/admin/shop")
public class ShopController {
    @Autowired
    private RedisTemplate redisTemplate;
    //店铺状态常数KEY
    public static final String KEY = "SHOP_STATUS";

    /**
     * 获取营业状态
     *
     * @return
     */
    @ApiOperation("获取营业状态")
    @GetMapping("/status")
    public Result getShopStatus() {
        //获取 操作 String 类型（普通字符串）数据的接口
        ValueOperations valueOperations = redisTemplate.opsForValue();
        Object o = valueOperations.get(KEY);
        Integer status = (Integer) o;
        log.info("当前店铺的营业状态为：{}", status == 1 ? "营业中" : "打烊中");
        return Result.success(status);
    }

    /**
     * 设置营业状态
     * @param status
     * @return
     */
    @ApiOperation("设置营业状态")
    @PutMapping("/{status}")
    public Result setShopStatus(@PathVariable Integer status){
        log.info("设置当前营业状态为：{}",status);
        ValueOperations valueOperations = redisTemplate.opsForValue();
        valueOperations.set(KEY,status);
        return Result.success();
    }
}
