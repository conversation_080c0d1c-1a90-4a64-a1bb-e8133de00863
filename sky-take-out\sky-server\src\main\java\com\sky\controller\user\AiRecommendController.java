package com.sky.controller.user;

import com.sky.dto.AiRecommendDTO;
import com.sky.result.Result;
import com.sky.service.AiRecommendService;
import com.sky.vo.AiRecommendVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * AI推荐控制器
 */
@RestController
@RequestMapping("/user/ai")
@Api(tags = "AI智能推荐相关接口")
@Slf4j
public class AiRecommendController {

    @Autowired
    private AiRecommendService aiRecommendService;

    /**
     * 获取AI推荐菜品
     */
    @PostMapping("/recommend")
    @ApiOperation("获取AI推荐菜品")
    public Result<List<AiRecommendVO>> getRecommendations(@RequestBody AiRecommendDTO aiRecommendDTO) {
        log.info("AI推荐请求：{}", aiRecommendDTO);
        
        try {
            List<AiRecommendVO> recommendations = aiRecommendService.getRecommendations(aiRecommendDTO);
            log.info("AI推荐结果数量：{}", recommendations.size());
            
            return Result.success(recommendations);
        } catch (Exception e) {
            log.error("AI推荐失败", e);
            return Result.error("AI推荐服务暂时不可用，请稍后重试");
        }
    }

    /**
     * 获取所有可用菜品（用于测试）
     */
    @GetMapping("/dishes")
    @ApiOperation("获取所有可用菜品")
    public Result<List<AiRecommendVO>> getAllDishes() {
        log.info("获取所有可用菜品");

        try {
            List<AiRecommendVO> dishes = aiRecommendService.getAllAvailableDishes();
            return Result.success(dishes);
        } catch (Exception e) {
            log.error("获取菜品失败", e);
            return Result.error("获取菜品数据失败");
        }
    }

    /**
     * 测试AI响应解析（开发测试用）
     */
    @PostMapping("/test-parse")
    @ApiOperation("测试AI响应解析")
    public Result<String> testParse(@RequestBody Map<String, String> request) {
        log.info("测试AI响应解析");

        try {
            String mockResponse = request.get("aiResponse");
            if (mockResponse == null) {
                mockResponse = "推荐菜品1：[1] 清蒸鲈鱼 - 清淡口味，营养丰富，适合老人食用\n" +
                              "推荐菜品2：[3] 素炒时蔬 - 新鲜蔬菜，清爽不油腻，符合健康需求";
            }

            log.info("模拟AI响应：{}", mockResponse);
            return Result.success("解析测试完成，请查看日志");
        } catch (Exception e) {
            log.error("测试失败", e);
            return Result.error("测试失败");
        }
    }
}
