package com.sky.controller.user;

import com.sky.result.Result;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 客户端店铺操作接口
 */
@Slf4j
@RequestMapping("/user/shop")
//注意不能重名
@RestController("userShopController")
@Api(tags = "客户端店铺操作接口")
public class ShopController {
    public static final String KEY = "SHOP_STATUS";
    @Autowired
    private RedisTemplate redisTemplate;
    /**
     * 获取营业状态
     * @return
     */
    @GetMapping("/status")
    public Result getShopStatus(){
        ValueOperations valueOperations = redisTemplate.opsForValue();
        Object o = valueOperations.get(KEY);
        Integer status= (Integer) o;
        log.info("当前店铺的营业状态为：{}",status==1?"营业中":"打烊中");
        return Result.success(status);
    }
}
