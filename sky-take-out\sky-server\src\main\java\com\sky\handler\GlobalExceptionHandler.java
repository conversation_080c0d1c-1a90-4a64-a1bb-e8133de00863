package com.sky.handler;

import com.sky.constant.MessageConstant;
import com.sky.exception.BaseException;
import com.sky.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.bridge.Message;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.sql.SQLIntegrityConstraintViolationException;

/**
 * 全局异常处理器，处理项目中抛出的业务异常
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    /**
     * 捕获业务异常
     * @param ex
     * @return
     */
    @ExceptionHandler
    public Result exceptionHandler(BaseException ex){
        log.error("异常信息：{}", ex.getMessage());
        return Result.error(ex.getMessage());
    }

    /**
     * 重复名异常处理器
     * @return
     */
    @ExceptionHandler
    public Result doSQLException(SQLIntegrityConstraintViolationException se){
        log.error("出现异常：{}",se.getMessage());
        String message=se.getMessage();
        //如果异常包含重复无效用户名
        if(message.contains("Duplicate")){
            String[] str=message.split(" ");
            message=str[2];
            return Result.error(message+MessageConstant.ALREADY_EXIST);
        }

        return Result.error(MessageConstant.UNKNOWN_ERROR);
    }
}
