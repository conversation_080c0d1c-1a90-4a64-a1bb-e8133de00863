package com.sky.mapper;

import com.sky.anno.AutoFill;
import com.sky.entity.DishFlavor;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface DishFlavorMapper {

    /**
     * 添加口味
     * @param flavors
     */
    //因为是循环插入多条数据，所以要写入xml文件
    void insert(List<DishFlavor> flavors);

    /**
     * 删除菜品口味
     * @param ids
     */
    void deleteDishFlavorById(List<Long> ids);

    /**
     * 查询id菜品的口味
     * @param id
     * @return
     */
    @Select("select * from dish_flavor where dish_id=#{id}")
    List<DishFlavor> getFlavorsByDishId(Long id);

}
