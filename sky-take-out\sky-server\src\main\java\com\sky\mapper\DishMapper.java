package com.sky.mapper;

import com.github.pagehelper.Page;
import com.sky.anno.AutoFill;
import com.sky.dto.DishPageQueryDTO;
import com.sky.entity.Dish;
import com.sky.enumeration.OperationType;
import com.sky.vo.DishVO;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;


public interface DishMapper {

    /**
     * 根据分类id查询菜品数量
     *
     * @param categoryId
     * @return
     */
    @Select("select count(id) from dish where category_id = #{categoryId}")
    Integer countByCategoryId(Long categoryId);

    /**
     * 新增菜品
     *
     * @param dish
     */
    @AutoFill(OperationType.INSERT)
    @Options(useGeneratedKeys = true, keyProperty = "id")
    @Insert("insert into dish values (null,#{name},#{categoryId},#{price},#{image},#{description},#{status},#{createTime},#{updateTime},#{createUser},#{updateUser})")
    void insert(Dish dish);

    /**
     * 菜品分页查询
     *
     * @param dto
     * @return
     */
    Page<DishVO> list(DishPageQueryDTO dto);

    /**
     * 查找菜品
     *
     * @param id
     * @return
     */
    @Select("select status from dish where id = #{id}")
    Integer selectByDishId(Long id);

    /**
     * 删除菜品
     *
     * @param ids
     */
    void deleteDishById(List<Long> ids);

    /**
     * 根据id查询菜品
     *
     * @param id
     * @return
     */
    @Select("select * from dish where id=#{id}")
    Dish getByDishId(Long id);

    /**
     * 更新菜品基础数据
     *
     * @param dish
     */
    @AutoFill(OperationType.UPDATE)
    void updateDish(Dish dish);

    /**
     * 根据分类id查询菜品
     *
     * @param categoryId
     * @return
     */
    @Select("select * from dish where category_id = #{categoryId}")
    List<DishVO> selectDishByCategoryId(Long categoryId);

    /**
     * 菜品起售、停售
     *
     * @param status
     * @param id
     * @return
     */
    @Update("update dish set status=#{status} where id=#{id}")
    void updateStatus(Integer status, Long id);


    /**
     * 根据dish_id查找setmeal_id
     *
     * @param id
     * @return
     */
    @Select("select setmeal_id from setmeal_dish where dish_id=#{id}")
    Long selectSetmealIdByDishId(Long id);

    /**
     * 用户端根据分类id和菜品起售状况查询菜品列表
     *
     * @param dish
     * @return
     */
    @Select("select * from dish where category_id=#{categoryId} and status = #{status}")
    List<Dish> listOfDish(Dish dish);

    /**
     * 根据条件统计菜品数量
     *
     * @param map
     * @return
     */
    Integer countByMap(Map map);

    /**
     * 查询所有起售状态的菜品（用于AI推荐）
     *
     * @return
     */
    @Select("select d.*, c.name as categoryName from dish d left join category c on d.category_id = c.id where d.status = 1")
    List<DishVO> listAllAvailableDishes();

    /**
     * 查询所有起售状态的菜品（不带分类条件）
     *
     * @return
     */
    @Select("select * from dish where status = 1")
    List<Dish> listAllEnabledDishes();
}
