package com.sky.mapper;

import com.github.pagehelper.Page;
import com.sky.dto.GoodsSalesDTO;
import com.sky.dto.OrdersPageQueryDTO;
import com.sky.entity.Orders;
import com.sky.vo.OrderVO;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface OrdersMapper {
    /**
     * 插入订单表数据
     * @param orders
     */
    void insert(Orders orders);

    /**
     * 根据订单号和用户id查询订单
     * @param orderNumber
     * @param userId
     */
    @Select("select * from orders where number = #{orderNumber} and user_id= #{userId}")
    Orders getByNumberAndUserId(String orderNumber, Long userId);

    /**
     * 修改订单信息
     * @param orders
     */
    void update(Orders orders);

    /**
     * 查询当前用户的订单信息
     * @param userId
     * @return
     */
    Page<OrderVO> selectOrders(Long userId,Integer status);

    /**
     * 根据id查询订单
     * @param id
     * @return
     */
    @Select("select * from orders where id = #{id}")
    Orders selectById(Long id);

    /**
     * 条件订单搜索
     * @param ordersPageQueryDTO
     * @return
     */
    Page<Orders> pageQuery(OrdersPageQueryDTO ordersPageQueryDTO);

    /**
     * 根据状态统计订单数量
     * @param status
     */
    @Select("select count(id) from orders where status = #{status}")
    Integer countStatus(Integer status);

    /**
     * 查询等待时间超过15分钟（900s）的待付款订单
     * @param pendingPayment
     * @param localDateTime
     * @return
     */
    //注意相减语法TIMESTAMPDIFF(unit, datetime1, datetime2)datetime2 - datetime1 的差值，单位由 unit 指定（如 SECOND、MINUTE 等）。
    @Select("select * from orders where status = #{pendingPayment} and TIMESTAMPDIFF(MINUTE,order_time,#{localDateTime}) > 15")
    List<Orders> selectTimeOutOrder(Integer pendingPayment, LocalDateTime localDateTime);

    /**
     * 查询是否有订单还处于“派送中”，如果有则修改为已完成
     * @param deliveryInProgress
     * @return
     */
    @Select("select * from orders where status = #{deliveryInProgress}")
    List<Orders> selectByStatus(Integer deliveryInProgress);

    /**
     * 根据起始时间或状态查询订单数量
     * @param map
     * @return
     */
    Integer selectByTimeOrStatus(Map map);
    /**
     * 根据订单状态为已完成和时间区间获取营业额
     * @param map
     * @return
     */
    @Select("select sum(amount) from orders where status = #{status} and order_time between #{begin} and #{end}")
    Double selectAmountByMap(Map map);

    /**
     * 查询商品销量排名
     * @param beginTime
     * @param endTime
     */
    List<GoodsSalesDTO> getSalesTop10(LocalDateTime beginTime, LocalDateTime endTime);
}
