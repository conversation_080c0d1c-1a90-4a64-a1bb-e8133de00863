package com.sky.mapper;

import com.github.pagehelper.Page;
import com.sky.anno.AutoFill;
import com.sky.dto.SetmealPageQueryDTO;
import com.sky.entity.Setmeal;
import com.sky.entity.SetmealDish;
import com.sky.enumeration.OperationType;
import com.sky.vo.DishItemVO;
import com.sky.vo.SetmealVO;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;


public interface SetmealMapper {

    /**
     * 根据分类id查询套餐的数量
     * @param id
     * @return
     */
    @Select("select count(id) from setmeal where category_id = #{categoryId}")
    Integer countByCategoryId(Long id);

    /**
     * 根据菜品id查询套餐id
     *
     * @param id
     * @return
     */
    @Select("select setmeal_id from setmeal_dish where dish_id=#{id}")
    List<Long> selectSetmealIdByDishId(Long id);

    /**
     * 新增套餐
     * @param setmeal
     */
    @AutoFill(OperationType.INSERT)
    @Options(useGeneratedKeys = true,keyProperty = "id")
    @Insert("insert into setmeal values (null,#{categoryId},#{name},#{price},#{status},#{description},#{image},#{createTime},#{updateTime},#{createUser},#{updateUser})")
    void insertSetmeal(Setmeal setmeal);

    /**
     * 新增关联表setmeal_dish的信息
     * @param setmealDishe
     */
    @Insert("insert into setmeal_dish values (null,#{setmealId},#{dishId},#{name},#{price},#{copies})")
    void insertSetmealDish(SetmealDish setmealDishe);

    /**
     * 分页查询
     * @param dto
     * @return
     */
    Page<SetmealVO> selectPageHelper(SetmealPageQueryDTO dto);

    /**
     * 批量删除套餐
     * @param id
     */
    @Delete("delete from setmeal where id=#{id}")
    void deleteBatchWithSetmeal(Long id);

    /**
     * 批量删除套餐关联的菜品
     * @param id
     */
    @Delete("delete from setmeal_dish where setmeal_id =#{id}")
    void deleteBatchWithSetmealDish(Long id);

    /**
     * 根据id查询套餐
     * @param id
     * @return
     */
    SetmealVO selectSetmealById(Long id);

    /**
     * 根据setmealId查询setmeal_dish表的全部信息
     * @param id
     * @return
     */
    @Select("select * from setmeal_dish where setmeal_id=#{id}")
    List<SetmealDish> selectBySetmealId(Long id);


    /**
     * 套餐起售或停售
     * @param status
     * @param id
     */
    @Update("update setmeal set status=#{status} where id=#{id}")
    void updateStatus(Integer status, Long id);

    /**
     * 更新setmeal表的数据
     * @param setmeal
     */
    @AutoFill(OperationType.UPDATE)
    void updateSetmeal(Setmeal setmeal);

    /**
     * 动态条件查询套餐
     * @param setmeal
     * @return
     */
    List<Setmeal> list(Setmeal setmeal);

    /**
     * 根据套餐id查询菜品选项
     * @param setmealId
     * @return
     */
    @Select("select sd.name, sd.copies, d.image, d.description " +
            "from setmeal_dish sd left join dish d on sd.dish_id = d.id " +
            "where sd.setmeal_id = #{setmealId}")
    List<DishItemVO> getDishItemBySetmealId(Long setmealId);

    /**
     * 根据条件统计套餐数量
     * @param map
     * @return
     */
    Integer countByMap(Map map);
}
