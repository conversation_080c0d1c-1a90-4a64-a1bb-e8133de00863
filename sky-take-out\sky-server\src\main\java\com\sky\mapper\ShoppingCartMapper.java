package com.sky.mapper;

import com.sky.dto.ShoppingCartDTO;
import com.sky.entity.ShoppingCart;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface ShoppingCartMapper {
    /**
     * 根据userId和套餐、菜品、口味信息查询购物车
     * @param shoppingCart
     * @return
     */
    ShoppingCart selectByIdAndFoods(ShoppingCart shoppingCart);

    @Update("update shopping_cart set number = #{number} where id = #{id}")
    void update(ShoppingCart cart);

    @Insert("insert into shopping_cart (id, name, image, user_id, dish_id, setmeal_id, dish_flavor, number, amount, create_time) values " +
            "(null,#{name},#{image},#{userId},#{dishId},#{setmealId},#{dishFlavor},#{number},#{amount},#{createTime})")
    void insert(ShoppingCart shoppingCart);

    /**
     * 查找购物车
     * @return
     */
    @Select("select * from shopping_cart where user_id = #{id}")
    List<ShoppingCart> list(Long id);

    /**
     * 清空购物车
     * @param id
     */
    @Delete("delete from shopping_cart where user_id = #{id}")
    void deleteShoppingCart(Long id);

    /**
     * 删除单个菜品（根据唯一的自增id删除）
     * @param id
     */
    @Delete("delete from shopping_cart where id = #{id}")
    void deleteByShoppingCartId(Long id);

    /**
     * 菜品数量减一
     * @param cart
     */
    @Update("update shopping_cart set number = #{number} where id = #{id}")
    void updateForSub(ShoppingCart cart);

    /**
     * 批量插入购物车数据
     *
     * @param shoppingCartList
     */
    void insertBatch(List<ShoppingCart> shoppingCartList);
}
