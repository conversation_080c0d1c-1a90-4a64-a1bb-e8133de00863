package com.sky.mapper;

import com.sky.entity.User;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.Map;

public interface UserMapper {

    /**
     * 根据oepnid查找用户
     * @param openid
     * @return
     */
    @Select("select * from user where openid = #{openid}")
    User selectUserByOpenId(String openid);


    @Options(useGeneratedKeys = true,keyProperty = "id")
    @Insert("insert into user(id, openid, name, create_time) values (null,#{openid},#{name},#{createTime})")
    void insertUser(User user);

    /**
     * 根据id查询用户信息
     * @param id
     * @return
     */
    @Select("select * from user where id =#{id}")
    User selectUserById(Long id);

    /**
     * 根据当前时间查询之前的用户总数
     * @param map
     * @return
     */
    Integer selectAllUsersByTimes(Map map);
}
