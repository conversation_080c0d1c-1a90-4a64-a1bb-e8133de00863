package com.sky.service;

import com.sky.dto.AiRecommendDTO;
import com.sky.vo.AiRecommendVO;
import java.util.List;

/**
 * AI推荐服务接口
 */
public interface AiRecommendService {

    /**
     * 获取AI推荐菜品
     * @param aiRecommendDTO 推荐请求参数
     * @return 推荐结果列表
     */
    List<AiRecommendVO> getRecommendations(AiRecommendDTO aiRecommendDTO);

    /**
     * 获取所有可用菜品（用于AI分析）
     * @return 菜品列表
     */
    List<AiRecommendVO> getAllAvailableDishes();
}
