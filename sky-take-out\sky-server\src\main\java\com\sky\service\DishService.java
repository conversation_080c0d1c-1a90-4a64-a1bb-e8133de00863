package com.sky.service;

import com.sky.dto.DishDTO;
import com.sky.dto.DishPageQueryDTO;
import com.sky.entity.Dish;
import com.sky.result.PageResult;
import com.sky.vo.DishVO;

import java.util.List;

public interface DishService {
    /**
     * 新增菜品
     * @param dishDTO
     */
    void addDish(DishDTO dishDTO);

    /**
     * 菜品分页查询
     * @param dto
     * @return
     */
    PageResult list(DishPageQueryDTO dto);

    /**
     * 删除菜品
     * @param ids
     */
    void deleteDish(List<Long> ids);

    /**
     * 根据id查询菜品（数据回显）
     *
     * @param id
     * @return
     */
    DishVO getDishById(Long id);

    /**
     * 修改菜品
     * @param dto
     */
    void updateDish(DishDTO dto);

    /**
     * 根据分类id查询菜品
     *
     * @param categoryId
     * @return
     */
    List<DishVO> getDishByCategoryId(Long categoryId);

    /**
     * 菜品起售、停售服务接口
     * @param status
     * @param id
     */
    void enableOrDisable(Integer status, Long id);

    /**
     * 条件查询菜品和口味
     * @param dish
     * @return
     */
    List<DishVO> listWithFlavor(Dish dish);
}
