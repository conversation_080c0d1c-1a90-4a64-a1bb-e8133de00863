package com.sky.service;

import com.sky.dto.SetmealDTO;
import com.sky.dto.SetmealPageQueryDTO;
import com.sky.entity.Setmeal;
import com.sky.result.PageResult;
import com.sky.vo.DishItemVO;
import com.sky.vo.SetmealVO;

import java.io.Serializable;
import java.util.List;

/**
 * 套餐管理服务接口
 */
public interface SetmealService {
    /**
     * 新增套餐服务接口
     * @param dto
     */
    void addSetmeal(SetmealDTO dto);

    /**
     * 新增分页查询服务接口
     * @param dto
     * @return
     */
    PageResult page(SetmealPageQueryDTO dto);

    /**
     * 批量删除套餐服务接口
     * @param ids
     */
    void deleteBatch(List<Long> ids);

    /**
     * 根据id查询套餐服务接口
     * @param id
     * @return
     */
    SetmealVO getSetmealById(Long id);

    /**
     * 套餐起售或停售服务接口
     * @param status
     * @param id
     */
    void enableOrDisable(Integer status, Long id);

    /**
     * 修改套餐服务接口
     * @param dto
     * @return
     */
    void updateSetmeal(SetmealDTO dto);

    /**
     * 条件查询
     * @param setmeal
     * @return
     */
    List<Setmeal> list(Setmeal setmeal);

    /**
     * 根据id查询菜品选项
     * @param id
     * @return
     */
    List<DishItemVO> getDishItemById(Long id);
}
