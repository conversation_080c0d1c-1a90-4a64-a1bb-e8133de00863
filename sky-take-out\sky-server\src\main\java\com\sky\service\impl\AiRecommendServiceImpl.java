package com.sky.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sky.constant.StatusConstant;
import com.sky.dto.AiRecommendDTO;
import com.sky.entity.Dish;
import com.sky.entity.DishFlavor;
import com.sky.mapper.DishFlavorMapper;
import com.sky.mapper.DishMapper;
import com.sky.service.AiRecommendService;
import com.sky.service.DishService;
import com.sky.vo.AiRecommendVO;
import com.sky.vo.DishVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * AI推荐服务实现类
 */
@Slf4j
@Service
public class AiRecommendServiceImpl implements AiRecommendService {

    @Autowired
    private DishMapper dishMapper;

    @Autowired
    private DishFlavorMapper dishFlavorMapper;

    @Autowired
    private DishService dishService;

    @Autowired
    private RestTemplate restTemplate;

    // 豆包AI配置
    private static final String DOUBAO_API_URL = "https://ark.cn-beijing.volces.com/api/v3/chat/completions";
    private static final String API_KEY = "d10fee67-1a3b-4818-9a69-3470163cbbbf";
    private static final String MODEL = "doubao-seed-1-6-flash-250715";

    @Override
    public List<AiRecommendVO> getRecommendations(AiRecommendDTO aiRecommendDTO) {
        try {
            log.info("开始AI推荐，用户输入：{}，选择标签：{}", aiRecommendDTO.getUserInput(), aiRecommendDTO.getSelectedTags());

            // 1. 获取所有可用菜品
            List<AiRecommendVO> allDishes = getAllAvailableDishes();
            if (allDishes.isEmpty()) {
                log.warn("没有可用的菜品数据");
                return new ArrayList<>();
            }

            // 2. 构建AI提示词
            String prompt = buildPrompt(aiRecommendDTO, allDishes);
            log.info("构建的AI提示词：{}", prompt);

            // 3. 调用豆包AI
            String aiResponse = callDoubaoAI(prompt);
            log.info("豆包AI响应：{}", aiResponse);

            // 4. 解析AI响应
            List<AiRecommendVO> recommendations = parseAiResponse(aiResponse, allDishes);
            log.info("解析后的推荐结果数量：{}", recommendations.size());

            // 打印每个推荐结果的详细信息
            for (int i = 0; i < recommendations.size(); i++) {
                AiRecommendVO rec = recommendations.get(i);
                log.info("推荐{}：ID={}, 名称={}, 理由={}, 分数={}",
                    i+1, rec.getDishId(), rec.getName(), rec.getRecommendReason(), rec.getScore());
            }

            // 5. 限制返回数量
            int limit = aiRecommendDTO.getLimit() != null ? aiRecommendDTO.getLimit() : 5;
            List<AiRecommendVO> finalResult = recommendations.stream().limit(limit).collect(Collectors.toList());
            log.info("最终返回{}个推荐结果", finalResult.size());

            return finalResult;

        } catch (Exception e) {
            log.error("AI推荐失败，使用备用推荐策略", e);
            // 降级到简单推荐策略
            return getFallbackRecommendations(aiRecommendDTO);
        }
    }

    @Override
    public List<AiRecommendVO> getAllAvailableDishes() {
        try {
            // 查询所有起售状态的菜品
            List<Dish> dishes = dishMapper.listAllEnabledDishes();

            // 转换为DishVO并获取规格信息
            List<DishVO> dishVOList = new ArrayList<>();
            for (Dish dish : dishes) {
                DishVO dishVO = new DishVO();
                // 复制基本属性
                dishVO.setId(dish.getId());
                dishVO.setName(dish.getName());
                dishVO.setCategoryId(dish.getCategoryId());
                dishVO.setPrice(dish.getPrice());
                dishVO.setImage(dish.getImage());
                dishVO.setDescription(dish.getDescription());
                dishVO.setStatus(dish.getStatus());
                dishVO.setUpdateTime(dish.getUpdateTime());

                // 获取规格信息
                List<DishFlavor> flavors = dishFlavorMapper.getFlavorsByDishId(dish.getId());
                dishVO.setFlavors(flavors);

                dishVOList.add(dishVO);
            }

            return dishVOList.stream()
                    .map(this::convertToAiRecommendVO)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取菜品数据失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 构建AI提示词
     */
    private String buildPrompt(AiRecommendDTO dto, List<AiRecommendVO> dishes) {
        StringBuilder dishesInfo = new StringBuilder();
        for (AiRecommendVO dish : dishes) {
            dishesInfo.append(String.format("%d. %s(%.2f元): %s\n",
                dish.getDishId(), dish.getName(), dish.getPrice(),
                dish.getDescription() != null ? dish.getDescription() : "暂无描述"));
        }

        String selectedTags = dto.getSelectedTags() != null && !dto.getSelectedTags().isEmpty()
            ? String.join(", ", dto.getSelectedTags()) : "无";

        StringBuilder prompt = new StringBuilder();
        prompt.append("你是一个专业的美食推荐助手。请根据用户的需求，从以下菜品中推荐最合适的3-5道菜，并说明推荐理由。\n\n");
        prompt.append("可选菜品：\n").append(dishesInfo.toString()).append("\n");
        prompt.append("用户需求：").append(dto.getUserInput() != null ? dto.getUserInput() : "无具体描述").append("\n");
        prompt.append("用户选择的偏好标签：").append(selectedTags).append("\n\n");
        prompt.append("请按以下格式推荐菜品：\n");
        prompt.append("推荐菜品1：菜品ID 菜品名称 - 推荐理由\n");
        prompt.append("推荐菜品2：菜品ID 菜品名称 - 推荐理由\n");
        prompt.append("...\n\n");
        prompt.append("要求：\n");
        prompt.append("1. 只能推荐上述菜品列表中的菜品\n");
        prompt.append("2. 推荐理由要简洁明了，符合用户需求\n");
        prompt.append("3. 按推荐度排序，推荐3-5道菜\n");
        prompt.append("4. 严格按照格式：推荐菜品X：菜品ID 菜品名称 - 推荐理由");

        return prompt.toString();
    }

    /**
     * 宽松的AI响应解析（备用方法）
     */
    private List<AiRecommendVO> parseAiResponseLoose(String aiResponse, List<AiRecommendVO> allDishes) {
        log.info("使用宽松解析模式");
        List<AiRecommendVO> result = new ArrayList<>();

        // 查找所有包含数字的行，可能是菜品ID
        String[] lines = aiResponse.split("\n");
        int score = 95;

        for (String line : lines) {
            line = line.trim();

            // 查找包含数字的行
            if (line.matches(".*\\d+.*")) {
                // 提取所有数字
                String[] numbers = line.replaceAll("[^\\d]", " ").trim().split("\\s+");
                for (String numStr : numbers) {
                    if (!numStr.isEmpty()) {
                        try {
                            Long dishId = Long.parseLong(numStr);

                            // 检查是否是有效的菜品ID
                            AiRecommendVO dish = allDishes.stream()
                                    .filter(d -> d.getDishId().equals(dishId))
                                    .findFirst()
                                    .orElse(null);

                            if (dish != null && result.stream().noneMatch(r -> r.getDishId().equals(dishId))) {
                                AiRecommendVO recommendDish = AiRecommendVO.builder()
                                        .dishId(dish.getDishId())
                                        .name(dish.getName())
                                        .description(dish.getDescription())
                                        .price(dish.getPrice())
                                        .image(dish.getImage())
                                        .categoryName(dish.getCategoryName())
                                        .status(dish.getStatus())
                                        .recommendReason("AI智能推荐")
                                        .score(score)
                                        .build();

                                result.add(recommendDish);
                                score -= 5;
                                log.info("宽松解析添加菜品：ID={}, 名称={}", dishId, dish.getName());

                                if (result.size() >= 3) break; // 最多3个
                            }
                        } catch (NumberFormatException e) {
                            // 忽略无效数字
                        }
                    }
                }
            }
        }

        log.info("宽松解析完成，共解析到{}个推荐", result.size());
        return result;
    }

    /**
     * 调用豆包AI
     */
    private String callDoubaoAI(String prompt) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(API_KEY);

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", MODEL);
        requestBody.put("temperature", 0.7);
        requestBody.put("max_tokens", 1000);
        requestBody.put("reasoning", false);

        List<Map<String, String>> messages = new ArrayList<>();

        Map<String, String> systemMessage = new HashMap<>();
        systemMessage.put("role", "system");
        systemMessage.put("content", "你是一个专业的美食推荐助手，擅长根据用户需求推荐合适的菜品。");
        messages.add(systemMessage);

        Map<String, String> userMessage = new HashMap<>();
        userMessage.put("role", "user");
        userMessage.put("content", prompt);
        messages.add(userMessage);
        requestBody.put("messages", messages);

        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
        
        ResponseEntity<String> response = restTemplate.postForEntity(DOUBAO_API_URL, entity, String.class);
        
        if (response.getStatusCode() == HttpStatus.OK) {
            JSONObject jsonResponse = JSON.parseObject(response.getBody());
            JSONArray choices = jsonResponse.getJSONArray("choices");
            if (choices != null && choices.size() > 0) {
                JSONObject firstChoice = choices.getJSONObject(0);
                JSONObject message = firstChoice.getJSONObject("message");
                return message.getString("content");
            }
        }
        
        throw new RuntimeException("豆包AI调用失败");
    }

    /**
     * 解析AI响应
     */
    private List<AiRecommendVO> parseAiResponse(String aiResponse, List<AiRecommendVO> allDishes) {
        try {
            log.info("开始解析AI响应，响应长度：{}", aiResponse.length());
            log.info("AI响应内容：\n{}", aiResponse);

            List<AiRecommendVO> result = new ArrayList<>();

            // 按行分割响应
            String[] lines = aiResponse.split("\n");
            log.info("AI响应共{}行", lines.length);

            int score = 95; // 初始分数，递减

            for (int i = 0; i < lines.length; i++) {
                String line = lines[i].trim();
                log.info("处理第{}行：{}", i+1, line);

                // 匹配格式：推荐菜品X：[菜品ID] 菜品名称 - 推荐理由 或 推荐菜品X：菜品ID 菜品名称 - 推荐理由
                if (line.contains("推荐菜品") && line.contains("-")) {
                    log.info("找到推荐行：{}", line);
                    try {
                        String dishIdStr = null;

                        // 方式1：尝试提取方括号中的ID [菜品ID]
                        if (line.contains("[") && line.contains("]")) {
                            int idStart = line.indexOf("[") + 1;
                            int idEnd = line.indexOf("]");
                            if (idStart > 0 && idEnd > idStart) {
                                dishIdStr = line.substring(idStart, idEnd).trim();
                                log.info("从方括号提取到菜品ID字符串：{}", dishIdStr);
                            }
                        }

                        // 方式2：如果没有方括号，从冒号后提取第一个数字
                        if (dishIdStr == null) {
                            int colonIndex = line.indexOf("：");
                            if (colonIndex > 0) {
                                String afterColon = line.substring(colonIndex + 1);
                                // 提取第一个连续的数字
                                String[] parts = afterColon.trim().split("\\s+");
                                if (parts.length > 0 && parts[0].matches("\\d+")) {
                                    dishIdStr = parts[0];
                                    log.info("从冒号后提取到菜品ID字符串：{}", dishIdStr);
                                }
                            }
                        }

                        if (dishIdStr != null) {
                            Long dishId = Long.parseLong(dishIdStr);
                            log.info("解析菜品ID：{}", dishId);

                            // 提取推荐理由
                            int reasonStart = line.indexOf("-") + 1;
                            String recommendReason = reasonStart > 0 && reasonStart < line.length()
                                ? line.substring(reasonStart).trim() : "AI推荐";
                            log.info("提取推荐理由：{}", recommendReason);

                            // 找到对应的菜品
                            AiRecommendVO dish = allDishes.stream()
                                    .filter(d -> d.getDishId().equals(dishId))
                                    .findFirst()
                                    .orElse(null);

                            if (dish != null) {
                                log.info("找到对应菜品：{}", dish.getName());

                                // 创建新的对象避免修改原对象
                                AiRecommendVO recommendDish = AiRecommendVO.builder()
                                        .dishId(dish.getDishId())
                                        .name(dish.getName())
                                        .description(dish.getDescription())
                                        .price(dish.getPrice())
                                        .image(dish.getImage())
                                        .categoryName(dish.getCategoryName())
                                        .status(dish.getStatus())
                                        .recommendReason(recommendReason)
                                        .score(score)
                                        .build();

                                result.add(recommendDish);
                                score -= 5; // 分数递减

                                log.info("成功添加推荐菜品：ID={}, 名称={}, 理由={}, 分数={}",
                                    dishId, dish.getName(), recommendReason, score + 5);
                            } else {
                                log.warn("未找到ID为{}的菜品", dishId);
                            }
                        } else {
                            log.warn("无法提取菜品ID从行：{}", line);
                        }
                    } catch (Exception e) {
                        log.warn("解析推荐行失败：{}", line, e);
                    }
                } else if (line.length() > 0) {
                    log.info("跳过非推荐行：{}", line);
                }
            }

            log.info("AI响应解析完成，共解析到{}个推荐", result.size());

            // 如果没有解析到任何推荐，尝试更宽松的解析
            if (result.isEmpty()) {
                log.warn("未解析到任何推荐，尝试宽松解析");
                result = parseAiResponseLoose(aiResponse, allDishes);
            }

            return result;

        } catch (Exception e) {
            log.error("解析AI响应失败", e);
            throw new RuntimeException("AI响应解析失败");
        }
    }

    /**
     * 备用推荐策略
     */
    private List<AiRecommendVO> getFallbackRecommendations(AiRecommendDTO dto) {
        List<AiRecommendVO> allDishes = getAllAvailableDishes();
        
        // 简单的关键词匹配推荐
        List<AiRecommendVO> recommendations = new ArrayList<>();
        String userInput = dto.getUserInput() != null ? dto.getUserInput().toLowerCase() : "";
        List<String> selectedTags = dto.getSelectedTags() != null ? dto.getSelectedTags() : new ArrayList<>();

        for (AiRecommendVO dish : allDishes) {
            int score = 60; // 基础分数
            StringBuilder reason = new StringBuilder("推荐理由：");

            // 名称匹配
            if (dish.getName().toLowerCase().contains(userInput)) {
                score += 20;
                reason.append("菜品名称符合您的需求；");
            }

            // 描述匹配
            if (dish.getDescription() != null && dish.getDescription().toLowerCase().contains(userInput)) {
                score += 15;
                reason.append("菜品特色符合您的偏好；");
            }

            // 标签匹配（简单实现）
            for (String tag : selectedTags) {
                if (dish.getName().contains(tag) || (dish.getDescription() != null && dish.getDescription().contains(tag))) {
                    score += 10;
                    reason.append("符合").append(tag).append("偏好；");
                }
            }

            if (score >= 70) {
                dish.setScore(score);
                dish.setRecommendReason(reason.toString());
                recommendations.add(dish);
            }
        }

        // 按分数排序并限制数量
        return recommendations.stream()
                .sorted((a, b) -> b.getScore().compareTo(a.getScore()))
                .limit(dto.getLimit() != null ? dto.getLimit() : 5)
                .collect(Collectors.toList());
    }

    /**
     * 转换DishVO为AiRecommendVO
     */
    private AiRecommendVO convertToAiRecommendVO(DishVO dish) {
        return AiRecommendVO.builder()
                .dishId(dish.getId())
                .name(dish.getName())
                .description(dish.getDescription())
                .price(dish.getPrice())
                .image(dish.getImage())
                .categoryName(dish.getCategoryName())
                .status(dish.getStatus())
                .type(1) // 默认为菜品类型
                .flavors(dish.getFlavors() != null ? dish.getFlavors() : new ArrayList<>())
                .dishNumber(0) // 默认购物车数量为0
                .build();
    }
}
