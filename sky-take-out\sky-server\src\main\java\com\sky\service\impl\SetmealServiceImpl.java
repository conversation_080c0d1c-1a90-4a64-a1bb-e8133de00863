package com.sky.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sky.constant.MessageConstant;
import com.sky.dto.SetmealDTO;
import com.sky.dto.SetmealPageQueryDTO;
import com.sky.entity.Dish;
import com.sky.entity.Setmeal;
import com.sky.entity.SetmealDish;
import com.sky.exception.DeletionNotAllowedException;
import com.sky.exception.SetmealEnableFailedException;
import com.sky.mapper.CategoryMapper;
import com.sky.mapper.DishMapper;
import com.sky.mapper.SetmealMapper;
import com.sky.result.PageResult;
import com.sky.service.SetmealService;
import com.sky.vo.DishItemVO;
import com.sky.vo.SetmealVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * 套餐管理服务层
 */
@Slf4j
@Service
public class SetmealServiceImpl implements SetmealService {
    @Autowired
    private SetmealMapper setmealMapper;
    @Autowired
    private CategoryMapper categoryMapper;
    @Autowired
    private SetmealService setmealService;
    @Autowired
    private DishMapper dishMapper;
    /**
     * 新增套餐接口
     * @param dto
     */
    @Override
    public void addSetmeal(SetmealDTO dto) {
        //新增一个套餐表实体对象
        Setmeal setmeal=new Setmeal();
        //dto的值先赋值给它
        BeanUtils.copyProperties(dto,setmeal);
        //调用mapper层的方法
        setmealMapper.insertSetmeal(setmeal);
        //还要更新关联表setmeal_dish，这个需要反射setmeal_id
        List<SetmealDish> setmealDishes =dto.getSetmealDishes();
        setmealDishes.forEach(setmealDish -> {
            //反射拿到setmealId即可赋值
            setmealDish.setSetmealId(setmeal.getId());
            setmealMapper.insertSetmealDish(setmealDish);
        });

    }

    /**
     * 套餐分页查询
     * @param dto
     * @return
     */
    @Override
    public PageResult page(SetmealPageQueryDTO dto) {
        //设置分页参数
        PageHelper.startPage(dto.getPage(),dto.getPageSize());
        //强转为Page对象，从mapper层中查询数据
        Page<SetmealVO> setmealVOPage=setmealMapper.selectPageHelper(dto);
        //返回一个分页结果给PageResult
        return new PageResult(setmealVOPage.getTotal(),setmealVOPage.getResult());
    }

    /**
     * 批量删除套餐
     * @param ids
     */
    //记得开启事务
    @Transactional
    @Override
    public void deleteBatch(List<Long> ids) {
        //先检查是否有套餐的状态是在售的，如果有在售的就抛出异常
        ids.forEach(id->{
            Integer status = setmealMapper.selectSetmealById(id).getStatus();
            if (status!=0){
                throw new DeletionNotAllowedException(MessageConstant.SETMEAL_ON_SALE);
            }
        });
        ids.forEach(id->{
            setmealMapper.deleteBatchWithSetmeal(id);
            setmealMapper.deleteBatchWithSetmealDish(id);
        });

    }

    /**
     * 根据id查询套餐服务实现
     * @param id
     * @return
     */
    @Override
    public SetmealVO getSetmealById(Long id) {
        //填充了套餐的信息
        SetmealVO setmealVO=setmealMapper.selectSetmealById(id);
        //根据分类id查询分类名称并设置
        setmealVO.setCategoryName(categoryMapper.selectCategoryNameById(setmealVO.getCategoryId()));
        //还要查询setmeal_dish表的全部数据！
        setmealVO.setSetmealDishes(setmealMapper.selectBySetmealId(id));
        return setmealVO;
    }

    /**
     * 套餐起售或停售
     * @param status
     * @param id
     */
    @Override
    public void enableOrDisable(Integer status, Long id) {
        //套餐内如果有停售菜品，则套餐无法上架
        //先查找一下setmeal_dish表，根据查出来的dish_id去遍历每个菜品的status
        List<SetmealDish> setmealDishes = setmealMapper.selectBySetmealId(id);
        setmealDishes.forEach(setmealDish -> {
            //根据查询到的菜品表再获取状态信息！
            Integer status1 = dishMapper.getByDishId(setmealDish.getDishId()).getStatus();
            if(status1==0){
                throw new SetmealEnableFailedException(MessageConstant.SETMEAL_ENABLE_FAILED);
            }
        });
        setmealMapper.updateStatus(status,id);
    }

    /**
     * 修改套餐
     * @param dto
     * @return
     */
    @Override
    public void updateSetmeal(SetmealDTO dto) {
        //与数据库交互更新，需要用到entity类
        //更新setmeal表
        Setmeal setmeal=new Setmeal();
        BeanUtils.copyProperties(dto,setmeal);
        setmealMapper.updateSetmeal(setmeal);
        //更新完成setmeal表，还要插入setmeal_dish表，先删后新增
        //取出setmealDish里面的内容
        List<SetmealDish> setmealDishes = dto.getSetmealDishes();

        //删除关联表setmeal_dish
        setmealMapper.deleteBatchWithSetmealDish(dto.getId());
        //新增关联表
        setmealDishes.forEach(setmealDish -> {
            setmealDish.setSetmealId(dto.getId());
            //前端貌似没有传递setmealId到setmealDish中
            setmealMapper.insertSetmealDish(setmealDish);
        });
    }
    /**
     * 条件查询
     * @param setmeal
     * @return
     */
    public List<Setmeal> list(Setmeal setmeal) {
        List<Setmeal> list = setmealMapper.list(setmeal);
        return list;
    }

    /**
     * 根据id查询菜品选项
     * @param id
     * @return
     */
    public List<DishItemVO> getDishItemById(Long id) {
        return setmealMapper.getDishItemBySetmealId(id);
    }
}
