package com.sky.service.impl;

import com.sky.context.BaseContext;
import com.sky.dto.ShoppingCartDTO;
import com.sky.entity.Dish;
import com.sky.entity.ShoppingCart;
import com.sky.mapper.DishMapper;
import com.sky.mapper.SetmealMapper;
import com.sky.mapper.ShoppingCartMapper;
import com.sky.service.ShoppingCartService;
import com.sky.vo.SetmealVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
public class ShoppingCartServiceImpl implements ShoppingCartService {
    @Autowired
    private ShoppingCartMapper shoppingCartMapper;
    @Autowired
    private SetmealMapper setmealMapper;
    @Autowired
    private DishMapper dishMapper;

    /**
     * 新增购物车
     *
     * @param shoppingCartDTO
     */
    @Override
    public void addCart(ShoppingCartDTO shoppingCartDTO) {
        //由于修改的表格是shopping_cart，应该交由对应的实体类去完成
        ShoppingCart shoppingCart = new ShoppingCart();
        //浅拷贝属性
        BeanUtils.copyProperties(shoppingCartDTO, shoppingCart);
        //1.判断是否有当前用户所添加的食品的购物车信息，商品加用户线程id进行判断
        shoppingCart.setUserId(BaseContext.getCurrentId());
        //cart里面有自动生成的id，这个是唯一的，后续可以更具这个字符进行更新数量操作
        ShoppingCart cart = shoppingCartMapper.selectByIdAndFoods(shoppingCart);
        if (cart == null) {//说明没有此商品的购物车信息
            //如果是套餐
            if (shoppingCart.getSetmealId() != null) {
                SetmealVO setmealVO = setmealMapper.selectSetmealById(shoppingCart.getSetmealId());
                shoppingCart.setName(setmealVO.getName());
                shoppingCart.setImage(setmealVO.getImage());
                shoppingCart.setAmount(setmealVO.getPrice());
            }
            //如果是菜品
            else {
                Dish dish = dishMapper.getByDishId(shoppingCart.getDishId());
                shoppingCart.setName(dish.getName());
                shoppingCart.setImage(dish.getImage());
                shoppingCart.setAmount(dish.getPrice());
            }
            shoppingCart.setNumber(1);
            shoppingCart.setCreateTime(LocalDateTime.now());
            //完成初始化数据之后插入shopping_cart中
            shoppingCartMapper.insert(shoppingCart);
        }
        //如果有信息，则number+1
        else {
            cart.setNumber(cart.getNumber() + 1);
            shoppingCartMapper.update(cart);
        }

    }

    /**
     * 查看购物车
     *
     * @return
     */
    @Override
    public List<ShoppingCart> list() {
        //从数据库中查找数据
        List<ShoppingCart> shoppingCart = shoppingCartMapper.list(BaseContext.getCurrentId());
        return shoppingCart;
    }

    /**
     * 清空购物车
     */
    @Override
    public void clean() {
        shoppingCartMapper.deleteShoppingCart(BaseContext.getCurrentId());
    }

    /**
     * 删除单个商品
     *
     * @param shoppingCartDTO
     */
    @Override
    public void sub(ShoppingCartDTO shoppingCartDTO) {
        //首先先查询当前用户的购物车数据
        ShoppingCart shoppingCart = new ShoppingCart();
        Long id = BaseContext.getCurrentId();
        BeanUtils.copyProperties(shoppingCartDTO,shoppingCart);
        shoppingCart.setUserId(id);
        List<ShoppingCart> list = shoppingCartMapper.list(id);
        if (list != null && list.size() > 0) {//如果有数据那么开始判断，否则就不进行任何操作
            ShoppingCart cart = shoppingCartMapper.selectByIdAndFoods(shoppingCart);
            if(cart.getNumber()==1){//如果数量为1，那么就删除
                shoppingCartMapper.deleteByShoppingCartId(cart.getId());
                log.info("购物车商品：{} 已删除",cart);
            }
            else if (cart.getNumber()>1){//如果数量大于1，那么就更新数据
                cart.setNumber(cart.getNumber()-1);
                log.info("当前购物车中的商品：{} 的数量减一",cart.getName());
                shoppingCartMapper.updateForSub(cart);
            }
        }
    }
}
