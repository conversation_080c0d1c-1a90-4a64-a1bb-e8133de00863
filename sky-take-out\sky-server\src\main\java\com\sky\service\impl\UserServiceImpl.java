package com.sky.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sky.constant.MessageConstant;
import com.sky.dto.UserLoginDTO;
import com.sky.entity.User;
import com.sky.exception.LoginFailedException;
import com.sky.mapper.UserMapper;
import com.sky.properties.WeChatProperties;
import com.sky.service.UserService;
import com.sky.utils.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class UserServiceImpl implements UserService {
    public static final String WX_LOGIN="https://api.weixin.qq.com/sns/jscode2session";
    @Autowired
    private WeChatProperties weChatProperties;
    @Autowired
    private UserMapper userMapper;
    /**
     * 用户登录
     *
     * @param dto
     * @return
     */
    @Override
    public User login(UserLoginDTO dto) {
        //1.使用HttpClientUtil工具类，对微信接口平台发送请求
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("appid",weChatProperties.getAppid());
        paramMap.put("secret",weChatProperties.getSecret());
        paramMap.put("js_code",dto.getCode());
        paramMap.put("grant_type","authorization_code");
        String res = HttpClientUtil.doGet(WX_LOGIN, paramMap);
        //2.解析请求的结果，并得到openid，用户唯一标识
        JSONObject jsonObject = JSON.parseObject(res);
        String openid = jsonObject.getString("openid");
        if(openid==null){
            throw new LoginFailedException(MessageConstant.USER_NOT_LOGIN);
        }
        //判断是否存在这个用户
        User user=userMapper.selectUserByOpenId(openid);
        //如果不存在，那么就插入数据，然后自动注册并返回数据
        if(user == null){
            user=new User();
            user.setOpenid(openid);
            user.setName(openid.substring(0,5));
            user.setCreateTime(LocalDateTime.now());
            userMapper.insertUser(user);
        }
        //如果存在，那么就返回数据
        return user;

    }
}
