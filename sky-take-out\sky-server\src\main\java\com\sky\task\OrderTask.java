package com.sky.task;

import com.sky.entity.Orders;
import com.sky.mapper.OrdersMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;


/**
 * 自定义定时任务，实现订单状态定时处理
 */
@Slf4j
@Component//交给Spring的Bean容器进行管理
public class OrderTask {
    @Autowired
    private OrdersMapper ordersMapper;

    /**
     * 处理支付超时订单
     */
    //通过定时任务每分钟检查一次是否存在支付超时订单（下单后超过15分钟仍未支付则判定为支付超时订单），如果存在则修改订单状态为“已取消”
    @Scheduled(cron = "0 * * * * ?")//每分钟的第0整秒执行一次
    public void processTimeoutOrder(){
        log.info("处理支付超时订单：{}", new Date());
        //获取本地时间
        LocalDateTime localDateTime=LocalDateTime.now();
        //获取等待时间超过15分钟（900s）的待付款订单，标记为已取消
        List<Orders> ordersList=ordersMapper.selectTimeOutOrder(Orders.PENDING_PAYMENT,localDateTime);
        if (ordersList!=null&&ordersList.size()>0){//如果存在超时订单则进行更新状态为已取消
            ordersList.forEach(order -> {
                order.setStatus(Orders.CANCELLED);
                //填充取消原因
                order.setCancelReason("支付超时，自动取消");
                //填充取消时间
                order.setCancelTime(LocalDateTime.now());
                ordersMapper.update(order);
            });
        }
    }

    /**
     * 处理“派送中”状态的订单
     */
    //通过定时任务每天凌晨1点检查一次是否存在“派送中”的订单，如果存在则修改订单状态为“已完成”
//    @Scheduled(cron = "0 * * * * ?")//测试时间
    @Scheduled(cron = "0 0 1 * * ?")//凌晨01：00检查
    public void processDeliveryOrder(){
        log.info("处理派送中订单：{}", new Date());
        //查询是否有订单还处于“派送中”，如果有则修改为已完成
        List<Orders> ordersList=ordersMapper.selectByStatus(Orders.DELIVERY_IN_PROGRESS);
        //如果有，则更新订单状态为已完成
        if (ordersList!=null&&ordersList.size()>0){
            ordersList.forEach(order->{
                //标记订单已完成，不用补充其他字段，送达时间不用补充
                order.setStatus(Orders.COMPLETED);
                ordersMapper.update(order);
            });
        }
    }


}
