sky:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    host: localhost
    port: 3306
    database: sky_take_out
    username: root
    password: 12345678
  jwt:
    #管理员jwt配置
    # 设置jwt签名加密时使用的秘钥
    admin-secret-key: sky-admin
    # 设置jwt过期时间
    admin-ttl: 7200000
    # 设置前端传递过来的令牌名称
    admin-token-name: token

    #用户jwt配置
    user-secret-key: sky-user
    user-ttl: 7200000
    #前端写的是authentication，不能随意更改
    user-token-name: authentication

  #阿里云oss配置
  alioss:
    endpoint: oss-cn-chengdu.aliyuncs.com
    access-key-id: LTAI5tPrvCvgJfv1iLM8wdaH
    access-key-secret: ******************************
    bucket-name: bingleng502-sky-take-out
  redis:
    host: localhost
    port: 6379
    database: 0
    password: 12345678

  wechat:
    appid: wxfce92bbc4a90d8cf
    secret: cfe74a40cf745d7d571c7b3c9f03d06b
    #微信支付相关配置---备注：下面的信息用不了
    mchid: 1561414331
    mchSerialNo: 4B3B3DC35414AD50B1B755BAF8DE9CC7CF407606
    privateKeyFilePath: D:\WeChatPay\apiclient_key.pem
    apiV3Key: CZBK51236435wxpay435434323FFDuv3
    weChatPayCertFilePath: D:\WeChatPay\wechatpay_166D96F876F45C7D07CE98952A96EC980368ACFC.pem
    #成功回调地址
    notifyUrl: https://268eeacd.r12.vip.cpolar.cn/notify/paySuccess
    #退款回调地址
    refundNotifyUrl: https://268eeacd.r12.vip.cpolar.cn/notify/refundXxx
  shop:
    address: 四川省德阳市罗江区大学路59号
  baidu:
    ak: yPLTYeZlHHxTEAAIMiJxg75L9NUxKpVy