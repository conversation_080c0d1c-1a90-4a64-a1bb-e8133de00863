<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.DishMapper">
    <update id="updateDish">
        update dish
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="price != null">price = #{price},</if>
            <if test="image != null">image = #{image},</if>
            <if test="description != null">description = #{description},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
        </set>
        where id=#{id}
    </update>
    <delete id="deleteDishById">
        delete from dish where id in
            <foreach collection="ids" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
    </delete>
    <select id="list" resultType="com.sky.vo.DishVO">
        select dish.*,category.name as categoryName from dish,category where dish.category_id=category.id
        <if test="name != null">
            and dish.name like concat('%',#{name},'%')
        </if>
        <if test="categoryId != null">
            and category.id= #{categoryId}
        </if>
        <if test="status!=null">
            and dish.status=#{status}
        </if>
        order by dish.create_time desc
    </select>
    <select id="countByMap" resultType="java.lang.Integer">
        select count(id) from dish
        <where>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="categoryId != null">
                and category_id = #{categoryId}
            </if>
        </where>
    </select>
</mapper>