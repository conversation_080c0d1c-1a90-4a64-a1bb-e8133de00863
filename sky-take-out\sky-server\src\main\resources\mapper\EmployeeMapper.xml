<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.EmployeeMapper">
    <update id="update">
        update employee
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="username != null">username = #{username},</if>
            <if test="password != null">password = #{password},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="idNumber != null">id_number = #{idNumber},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
            <if test="status != null">status = #{status},</if>
        </set>
        where id= #{id}
    </update>

    <!--  姓名模糊查询并降序输出  -->
    <select id="list" resultType="com.sky.entity.Employee">
        select * from employee
        <where>
            <if test="name != null and name!=''">
                name like concat('%',#{name},'%')
            </if>
        </where>
        order by create_time desc
    </select>
    <select id="getById" resultType="com.sky.entity.Employee">
        select *
        from employee
        where id = #{id}

    </select>
</mapper>
