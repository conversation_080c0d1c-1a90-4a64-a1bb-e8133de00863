<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.OrderDetailMapper">
    <!--批量插入订单详情-->
    <insert id="insertBatch">
        insert into order_detail (id, name, image, order_id, dish_id, setmeal_id, dish_flavor, number, amount) VALUES
        <foreach collection="orderDetailList" item="orderDetail" separator=",">
            (null,#{orderDetail.name},#{orderDetail.image},#{orderDetail.orderId},#{orderDetail.dishId},#{orderDetail.setmealId},#{orderDetail.dishFlavor},#{orderDetail.number},#{orderDetail.amount})
        </foreach>
    </insert>
</mapper>