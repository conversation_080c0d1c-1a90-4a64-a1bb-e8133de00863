<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.SetmealMapper">
    <!--  更新setmeal表数据  -->
    <update id="updateSetmeal">
        update setmeal
        <set>
            <if test="categoryId!=null">
                category_id=#{categoryId},
            </if>
            <if test="name!=null">
                name=#{name},
            </if>
            <if test="price!=null">
                price=#{price},
            </if>
            <if test="status!=null">
                status=#{status},
            </if>
            <if test="description!=null">
                description=#{description},
            </if>
            <if test="image!=null">
                image=#{image},
            </if>
            <if test="updateTime!=null">
                update_time=#{updateTime},
            </if>
            <if test="updateUser!=null">
                update_user=#{updateUser}
            </if>
        </set>
        where id=#{id}
    </update>
    <!--  分页查询  -->
    <select id="selectPageHelper" resultType="com.sky.vo.SetmealVO">
        select setmeal.*,category.name as categoryName from setmeal,category where category.id=setmeal.category_id
        <if test="name !=null">
            and setmeal.name like concat('%',#{name},'%')
        </if>
        <if test="categoryId !=null">
            and setmeal.category_id=#{categoryId}
        </if>
        <if test="status!=null">
            and setmeal.status=#{status}
        </if>
        order by setmeal.create_time desc
    </select>
    <!--  根据id查询套餐  -->
    <select id="selectSetmealById" resultType="com.sky.vo.SetmealVO">
        select *
        from setmeal
        where id = #{id}
    </select>

    <select id="list" parameterType="Setmeal" resultType="Setmeal">
        select * from setmeal
        <where>
            <if test="name != null">
                and name like concat('%',#{name},'%')
            </if>
            <if test="categoryId != null">
                and category_id = #{categoryId}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
        </where>
    </select>
    <select id="countByMap" resultType="java.lang.Integer">
        select count(id) from setmeal
        <where>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="categoryId != null">
                and category_id = #{categoryId}
            </if>
        </where>
    </select>
</mapper>