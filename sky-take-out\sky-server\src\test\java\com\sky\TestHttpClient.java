package com.sky;

import io.swagger.models.auth.In;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;
import org.junit.jupiter.api.Test;

import java.io.IOException;

//@SpringBootTest
//暂时用不到
public class TestHttpClient {
    //http://localhost:8080/user/shop/status GET方式请求
    @Test
    public void testGetShopStatus() throws Exception {
        //1.创建HttpClient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        //2.创建httpGet请求对象
        HttpGet httpGet = new HttpGet("http://localhost:8080/user/shop/status");
        //3.发送执行请求，接收相应结果
        CloseableHttpResponse response = httpClient.execute(httpGet);
        //4.解析请求
        Integer statusCode = response.getStatusLine().getStatusCode();
        System.out.println("服务端返回的状态码"+statusCode);

        HttpEntity entity = response.getEntity();//得到实体
        String string = EntityUtils.toString(entity);//解析成字符串
        System.out.println("服务端返回的数据是："+string);

        //5.关闭资源
        httpClient.close();
        response.close();
    }

    //http://localhost:8080/admin/employee/login在HttpClientTest中添加POST方式请求方法，
    // 相比GET请求来说，POST请求若携带参数需要封装请求体对象，并将该对象设置在请求对象中
    @Test
    public void testPostAdminEmployeeLogin() throws Exception {
        //1.创建HttpClient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        //2.创建HttpPost请求对象
        HttpPost httpPost = new HttpPost("http://localhost:8080/admin/employee/login");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("username","admin");
        jsonObject.put("password","123456");

        StringEntity stringEntity = new StringEntity(jsonObject.toString());//封装在String实体里面进行传输
        stringEntity.setContentEncoding("utf-8");//设置编码和响应格式
        stringEntity.setContentType("application/json");
        httpPost.setEntity(stringEntity);//设置请求对象


        //3.发送执行请求，接收相应结果
        CloseableHttpResponse response = httpClient.execute(httpPost);

        //4.解析数据
        Integer statusCode = response.getStatusLine().getStatusCode();
        System.out.println("响应状态码为:"+statusCode);

        HttpEntity entity = response.getEntity();
        String string = EntityUtils.toString(entity);
        System.out.println("响应数据:"+string);

        //5.关闭资源
        httpClient.close();
        response.close();
    }
}
