package com.sky.service;

import com.sky.dto.AiRecommendDTO;
import com.sky.vo.AiRecommendVO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * AI推荐服务测试类
 */
@SpringBootTest
public class AiRecommendServiceTest {

    @Test
    public void testBasicFunctionality() {
        // 基础功能测试
        AiRecommendDTO dto = new AiRecommendDTO();
        dto.setUserInput("想吃清淡的菜");
        dto.setSelectedTags(Arrays.asList("清淡", "健康"));
        dto.setLimit(5);

        // 验证DTO设置正确
        assert dto.getUserInput().equals("想吃清淡的菜");
        assert dto.getSelectedTags().size() == 2;
        assert dto.getLimit() == 5;

        System.out.println("基础功能测试通过");
    }

    @Test
    public void testAiResponseParsing() {
        // 测试AI响应解析
        String mockAiResponse = """
            根据您的需求，我为您推荐以下清淡菜品：

            推荐菜品1：[1] 清蒸鲈鱼 - 清淡口味，营养丰富，适合老人食用
            推荐菜品2：[3] 素炒时蔬 - 新鲜蔬菜，清爽不油腻，符合健康需求
            推荐菜品3：[4] 老火靓汤 - 滋补养生，口味清淡，营养价值高

            这些菜品都符合您的清淡口味要求。
            """;

        // 创建模拟菜品数据
        List<AiRecommendVO> mockDishes = Arrays.asList(
            AiRecommendVO.builder().dishId(1L).name("清蒸鲈鱼").price(new BigDecimal("68.00")).build(),
            AiRecommendVO.builder().dishId(3L).name("素炒时蔬").price(new BigDecimal("22.00")).build(),
            AiRecommendVO.builder().dishId(4L).name("老火靓汤").price(new BigDecimal("38.00")).build()
        );

        System.out.println("AI响应解析测试准备完成");
        System.out.println("模拟AI响应：" + mockAiResponse);
        System.out.println("模拟菜品数据：" + mockDishes.size() + "个");
    }
}
