<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.DishFlavorMapper">
    <!--  新增口味  -->
    <insert id="insert">
        insert into dish_flavor values
        <foreach collection="flavors" item="flavor" separator=",">
            (null,#{flavor.dishId},#{flavor.name},#{flavor.value})
        </foreach>
    </insert>
    <delete id="deleteDishFlavorById">
        delete from dish_flavor where dish_id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
    </delete>
</mapper>