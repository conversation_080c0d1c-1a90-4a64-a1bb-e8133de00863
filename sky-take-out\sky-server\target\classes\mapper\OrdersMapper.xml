<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.OrdersMapper">
    <!--插入订单信息-->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        insert into orders
        (number, status, user_id, user_name, address_book_id, order_time, checkout_time, pay_method, pay_status, amount,
         remark,
         phone, address, consignee, estimated_delivery_time, delivery_status, pack_amount, tableware_number,
         tableware_status)
        values (#{number}, #{status}, #{userId}, #{userName}, #{addressBookId}, #{orderTime}, #{checkoutTime},
                #{payMethod},
                #{payStatus}, #{amount}, #{remark}, #{phone}, #{address}, #{consignee},
                #{estimatedDeliveryTime}, #{deliveryStatus}, #{packAmount}, #{tablewareNumber}, #{tablewareStatus})
    </insert>

    <update id="update" parameterType="com.sky.entity.Orders">
        update orders
        <set>
            <if test="cancelReason != null and cancelReason!='' ">
                cancel_reason = #{cancelReason},
            </if>
            <if test="rejectionReason != null and rejectionReason!='' ">
                rejection_reason = #{rejectionReason},
            </if>
            <if test="cancelTime != null">
                cancel_time = #{cancelTime},
            </if>
            <if test="payStatus != null">
                pay_status = #{payStatus},
            </if>
            <if test="payMethod != null">
                pay_method = #{payMethod},
            </if>
            <if test="checkoutTime != null">
                checkout_time = #{checkoutTime},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="deliveryTime != null">
                delivery_time = #{deliveryTime}
            </if>
        </set>
        where id = #{id}
    </update>
    <!--历史订单分页查询-->
    <select id="selectOrders" resultType="com.sky.vo.OrderVO">
        select * from orders where user_id = #{userId}
        <if test="status!=null">
            and orders.status = #{status}
        </if>
    </select>
    <select id="pageQuery" resultType="com.sky.entity.Orders">
        select * from orders
        <where>
            <if test="beginTime !=null and endTime != null">
                and order_time between #{beginTime} and #{endTime}
            </if>
            <if test="number != null">
                and number = #{number}
            </if>
            <if test="phone != null">
                and phone = #{phone}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
        </where>
    </select>
    <select id="selectByTimeOrStatus" resultType="java.lang.Integer">
        select count(id) from orders
        <where>
            <if test="begin != null">
                order_time &gt; #{begin}
            </if>
            <if test="end != null">
                and order_time &lt; #{end}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
        </where>
    </select>
    <select id="getSalesTop10" resultType="com.sky.dto.GoodsSalesDTO">
        select od.name name,sum(od.number) number from order_detail od ,orders o
        where od.order_id = o.id
        and o.status = 5
        <if test="beginTime != null">
            and order_time &gt;= #{beginTime}
        </if>
        <if test="endTime != null">
            and order_time &lt;= #{endTime}
        </if>
        group by name
        order by number desc
        limit 0, 10
    </select>
</mapper>