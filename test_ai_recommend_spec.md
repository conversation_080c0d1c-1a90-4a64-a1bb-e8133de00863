# AI推荐页面规格选择功能测试

## 功能描述
AI推荐页面现在支持菜品规格选择功能，当推荐的菜品含有规格选择时，会显示"选择规格"按钮而不是直接显示"加入购物车"按钮。

## 修改内容

### 前端修改

#### 1. WXML模板修改 (mp-weixin/pages/aiRecommend/aiRecommend.wxml)
- 在推荐结果列表中添加了规格检查逻辑
- 有规格的菜品显示"选择规格"按钮
- 无规格的菜品显示"加入购物车"按钮
- 添加了规格选择弹窗

#### 2. JavaScript逻辑修改 (mp-weixin/pages/aiRecommend/aiRecommend.js)
- 添加了规格选择相关的数据字段：
  - `openMoreNormPop`: 规格选择弹窗显示状态
  - `moreNormDishdata`: 规格选择菜品数据
  - `moreNormdata`: 规格数据
  - `flavorDataes`: 已选择的规格数据

- 添加了规格选择相关的方法：
  - `moreNormDataesHandle()`: 打开规格选择弹窗
  - `closeMoreNorm()`: 关闭规格选择弹窗
  - `checkMoreNormPop()`: 选择规格选项
  - `addShop()`: 从规格选择弹窗加入购物车

- 修改了现有方法：
  - `addToCart()`: 增加规格检查，有规格的菜品引导用户选择规格
  - `openDetailHandle()`: 有规格的菜品直接打开规格选择弹窗
  - `handleBackendResponse()`: 数据转换时包含规格信息

#### 3. 样式修改 (mp-weixin/pages/aiRecommend/aiRecommend.wxss)
- 添加了规格选择弹窗的样式
- 添加了"选择规格"按钮的样式

### 后端修改

#### 1. AiRecommendVO类修改 (sky-take-out/sky-pojo/src/main/java/com/sky/vo/AiRecommendVO.java)
- 添加了`type`字段：菜品类型（1菜品 2套餐）
- 添加了`flavors`字段：菜品关联的口味规格
- 添加了`dishNumber`字段：购物车中的数量

#### 2. AiRecommendServiceImpl类修改 (sky-take-out/sky-server/src/main/java/com/sky/service/impl/AiRecommendServiceImpl.java)
- 修改了`getAllAvailableDishes()`方法，使用`dishService.listWithFlavor()`获取包含规格信息的菜品
- 修改了`convertToAiRecommendVO()`方法，转换时包含规格信息
- 添加了必要的依赖注入

## 测试场景

### 场景1：无规格菜品
1. 用户在AI推荐页面获取推荐结果
2. 对于无规格的菜品，显示"加入购物车"按钮
3. 点击"加入购物车"按钮，直接加入购物车

### 场景2：有规格菜品
1. 用户在AI推荐页面获取推荐结果
2. 对于有规格的菜品，显示"选择规格"按钮
3. 点击"选择规格"按钮，打开规格选择弹窗
4. 用户选择所需规格
5. 点击"加入购物车"按钮，将带规格的菜品加入购物车

### 场景3：规格选择验证
1. 打开规格选择弹窗
2. 不选择任何规格，直接点击"加入购物车"
3. 系统提示"请选择完整规格"
4. 选择完整规格后，成功加入购物车

## 预期效果
- AI推荐页面的菜品处理逻辑与主页保持一致
- 有规格的菜品必须通过规格选择才能加入购物车
- 用户体验更加统一和友好
- 避免了直接加入购物车时缺少规格信息的问题

## 注意事项
- 确保后端AI推荐接口返回的数据包含完整的规格信息
- 前端需要正确处理规格数据的格式
- 规格选择弹窗的样式需要与主页保持一致
- 错误处理和用户提示需要友好明确
