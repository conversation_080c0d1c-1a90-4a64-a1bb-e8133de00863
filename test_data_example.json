{"测试数据说明": "AI推荐页面规格选择功能测试数据", "无规格菜品示例": {"id": 1, "name": "白切鸡", "description": "嫩滑爽口的白切鸡", "price": 28.0, "image": "/static/dish1.jpg", "recommendReason": "口感清淡，适合老人食用", "score": 95, "tags": ["清淡口味"], "type": 1, "flavors": [], "dishNumber": 0}, "有规格菜品示例": {"id": 2, "name": "麻婆豆腐", "description": "经典川菜，麻辣鲜香", "price": 18.0, "image": "/static/dish2.jpg", "recommendReason": "经典川菜，口味丰富", "score": 90, "tags": ["麻辣口味"], "type": 1, "flavors": [{"id": 1, "dishId": 2, "name": "辣度", "value": "[\"不辣\",\"微辣\",\"中辣\",\"重辣\"]"}, {"id": 2, "dishId": 2, "name": "忌口", "value": "[\"不要葱\",\"不要蒜\",\"不要香菜\"]"}], "dishNumber": 0}, "前端处理后的规格数据示例": {"moreNormdata": [{"id": 1, "dishId": 2, "name": "辣度", "value": [{"text": "不辣", "selected": false}, {"text": "微辣", "selected": false}, {"text": "中辣", "selected": false}, {"text": "重辣", "selected": false}]}, {"id": 2, "dishId": 2, "name": "忌口", "value": [{"text": "不要葱", "selected": false}, {"text": "不要蒜", "selected": false}, {"text": "不要香菜", "selected": false}]}]}, "用户选择规格后的数据示例": {"flavorDataes": [{"name": "辣度", "value": "微辣"}, {"name": "忌口", "value": "不要葱"}]}, "加入购物车的数据格式": {"dishId": 2, "setmealId": null, "dishFlavor": "辣度:微辣,忌口:不要葱"}}